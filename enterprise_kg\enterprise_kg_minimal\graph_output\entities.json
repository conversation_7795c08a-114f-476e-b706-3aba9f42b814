{"vision": {"name": "VISION", "type": "Document", "source_documents": ["Culture Values - Rapid.pdf"], "created_at": "2025-06-01T14:42:17.702542"}, "table of contents": {"name": "Table of Contents", "type": "Document", "source_documents": ["Culture Values - Rapid.pdf"], "created_at": "2025-06-01T14:42:17.702542"}, "mission": {"name": "MISSION", "type": "Document", "source_documents": ["Culture Values - Rapid.pdf"], "created_at": "2025-06-01T14:42:17.702542"}, "core values": {"name": "CORE VALUES", "type": "Document", "source_documents": ["Culture Values - Rapid.pdf"], "created_at": "2025-06-01T14:42:17.702542"}, "company goals": {"name": "COMPANY GOALS", "type": "Document", "source_documents": ["Culture Values - Rapid.pdf"], "created_at": "2025-06-01T14:42:17.702984"}, "cultural pillars": {"name": "CULTURAL PILLARS", "type": "Document", "source_documents": ["Culture Values - Rapid.pdf"], "created_at": "2025-06-01T14:42:17.702984"}, "web3": {"name": "Web3", "type": "Technology", "source_documents": ["Culture Values - Rapid.pdf"], "created_at": "2025-06-01T14:42:17.702984"}, "blockchain": {"name": "blockchain", "type": "Technology", "source_documents": ["Culture Values - Rapid.pdf"], "created_at": "2025-06-01T14:42:17.702984"}, "priya sharma": {"name": "<PERSON><PERSON>", "type": "Person", "source_documents": ["JiraSource.json", "Title_ Project Atlas - AI-driven Customer Insights Platform.docx"], "created_at": "2025-06-01T14:42:22.261993"}, "atlas": {"name": "Atlas", "type": "Project", "source_documents": ["JiraSource.json"], "created_at": "2025-06-01T14:42:22.261993"}, "data ingestion": {"name": "Data Ingestion", "type": "System", "source_documents": ["JiraSource.json"], "created_at": "2025-06-01T14:42:22.261993"}, "ai analysis engine": {"name": "AI Analysis Engine", "type": "System", "source_documents": ["JiraSource.json"], "created_at": "2025-06-01T14:42:22.261993"}, "project atlas": {"name": "Project Atlas", "type": "Project", "source_documents": ["Title_ Project Atlas - AI-driven Customer Insights Platform.docx"], "created_at": "2025-06-01T14:42:32.973697"}, "data science": {"name": "Data Science", "type": "Team", "source_documents": ["Title_ Project Atlas - AI-driven Customer Insights Platform.docx"], "created_at": "2025-06-01T14:42:32.973697"}, "backend engineering": {"name": "Backend Engineering", "type": "Team", "source_documents": ["Title_ Project Atlas - AI-driven Customer Insights Platform.docx"], "created_at": "2025-06-01T14:42:32.973697"}, "customer success": {"name": "Customer Success", "type": "Team", "source_documents": ["Title_ Project Atlas - AI-driven Customer Insights Platform.docx"], "created_at": "2025-06-01T14:42:32.973697"}, "jira": {"name": "JIRA", "type": "Tool", "source_documents": ["Title_ Project Atlas - AI-driven Customer Insights Platform.docx"], "created_at": "2025-06-01T14:42:32.973697"}, "q2 2025": {"name": "Q2 2025", "type": "Quarter", "source_documents": ["Title_ Project Atlas - AI-driven Customer Insights Platform.docx"], "created_at": "2025-06-01T14:42:32.973697"}}