# Enterprise KG - Minimal Standalone Package

A minimal standalone enterprise knowledge graph system that extracts entities and relationships from documents and stores them in Neo4j. This package contains only the essential files needed for standalone operation.

**This is the minimal version** - contains only core functionality without integration examples, documentation, or optional features.

## Features

- **LLM-Powered Extraction**: Extract entities and relationships using OpenAI, Anthropic, or other LLM providers
- **Neo4j Integration**: Store knowledge graph data in Neo4j (including Neo4j Aura cloud)
- **Standalone Operation**: Run independently without external frameworks
- **Configurable Relationships**: Focus on specific relationship types (Person-involved_in-Project, etc.)
- **Document Processing**: Process Markdown, text, PDF, and Word (.docx) document formats

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements_standalone.txt
```

### 2. Set Up Environment Variables

```bash
# LLM API Key (choose one)
export OPENAI_API_KEY="your-openai-api-key"
# OR
export ANTHROPIC_API_KEY="your-anthropic-api-key"

# Neo4j Connection (for Neo4j Aura or local instance)
export NEO4J_URI="bolt://your-neo4j-instance:7687"
export NEO4J_USER="neo4j"
export NEO4J_PASSWORD="your-password"
```

### 3. Run the Processor

```bash
# Create sample documents and process them
python main.py --create-samples

# Process your own documents
python main.py --documents /path/to/your/documents

# Use with Neo4j Aura cloud instance
python main.py --neo4j-uri bolt://your-instance.databases.neo4j.io:7687 --neo4j-user neo4j --neo4j-password your-password
```

## Usage Examples

### Basic Usage
```bash
# Process documents in current directory
python main.py

# Create sample documents first
python main.py --create-samples --documents sample_docs
```

### Custom Configuration
```bash
# Use Anthropic Claude instead of OpenAI
python main.py --llm-provider anthropic --llm-model claude-3-5-sonnet-latest

# Focus on specific relationships
python main.py --focus-relationships involved_in mentions works_for

# Process only specific file types
python main.py --file-patterns .md .txt .pdf .docx

# Dry run (don't store in Neo4j)
python main.py --dry-run
```

### Neo4j Aura Cloud
```bash
# Connect to Neo4j Aura instance
python main.py \
  --neo4j-uri bolt://12345678.databases.neo4j.io:7687 \
  --neo4j-user neo4j \
  --neo4j-password your-aura-password
```

## Configuration Options

### Command Line Arguments

- `--documents`: Path to documents directory
- `--neo4j-uri`: Neo4j connection URI
- `--neo4j-user`: Neo4j username
- `--neo4j-password`: Neo4j password
- `--llm-provider`: LLM provider (openai, anthropic)
- `--llm-model`: LLM model name
- `--focus-relationships`: Relationship types to extract
- `--create-samples`: Create sample documents
- `--dry-run`: Process without storing data
- `--verbose`: Enable detailed logging

### Environment Variables

- `OPENAI_API_KEY`: OpenAI API key
- `ANTHROPIC_API_KEY`: Anthropic API key
- `NEO4J_URI`: Neo4j connection URI
- `NEO4J_USER`: Neo4j username
- `NEO4J_PASSWORD`: Neo4j password

## Extracted Relationships

The system focuses on these relationship types by default:

- **involved_in**: Person involved in Project
- **mentions**: Document mentions Entity
- **works_for**: Person works for Company
- **manages**: Person manages Team/Project
- **reports_to**: Person reports to Manager

## Neo4j Queries

After processing, query your knowledge graph:

```cypher
// View all relationships
MATCH (n)-[r]->(m)
RETURN n.name, type(r), m.name
LIMIT 20

// Find people involved in projects
MATCH (person:Entity)-[:INVOLVED_IN]->(project:Entity)
RETURN person.name, project.name

// Find management relationships
MATCH (person:Entity)-[:MANAGES]->(team:Entity)
RETURN person.name as Manager, team.name as Team

// Find all entities mentioned in a document
MATCH (doc:Entity)-[:MENTIONS]->(entity:Entity)
WHERE doc.name CONTAINS "report"
RETURN doc.name, entity.name
```

## Project Structure

```
enterprise_kg_minimal/
├── main.py                    # Main standalone script
├── standalone_processor.py    # Core processing logic
├── prompt_generator.py        # LLM prompt generation
├── test_config.py            # Configuration testing
├── .env                      # Environment configuration
├── requirements_standalone.txt # Dependencies
├── constants/                # Entity and relationship definitions
│   ├── __init__.py
│   ├── entities.py
│   ├── relationships.py
│   └── schemas.py
├── storage/                  # Neo4j client
│   ├── __init__.py
│   └── neo4j_client.py
├── utils/                    # Helper functions
│   ├── __init__.py
│   └── helpers.py
├── README.md                 # This file
└── ENV_SETUP.md             # Setup guide
```

**Total: 15 essential files** (vs 40+ in full version)

## Integration with Existing Systems

This minimal version can be easily integrated into existing systems:

```python
from enterprise_kg_minimal.standalone_processor import create_standalone_processor

# Create processor
processor = create_standalone_processor(
    neo4j_uri="bolt://your-neo4j:7687",
    neo4j_user="neo4j",
    neo4j_password="password",
    llm_provider="openai",
    llm_model="gpt-4o"
)

# Process documents
results = processor.process_directory("/path/to/documents")

# Process single document
metadata = processor.process_document("/path/to/document.md")
```

## Troubleshooting

### Neo4j Connection Issues
- Ensure Neo4j is running and accessible
- Check firewall settings for port 7687
- For Neo4j Aura, use the provided connection URI

### LLM API Issues
- Verify API keys are set correctly
- Check API rate limits and quotas
- Ensure sufficient API credits

### Processing Issues
- Check document file permissions
- Verify file formats are supported
- Review logs with `--verbose` flag

## Next Steps

1. **Test with Sample Data**: Use `--create-samples` to generate test documents
2. **Configure Neo4j**: Set up your Neo4j instance (local or Aura)
3. **Set API Keys**: Configure your preferred LLM provider
4. **Process Documents**: Run on your enterprise documents
5. **Query Results**: Explore the knowledge graph in Neo4j Browser

## Full Version Available

This is the **minimal standalone package**. For additional features, see the full version in the parent directory:

### Full Version Includes:
- **Integration Examples**: Hybrid search with existing Pinecone systems
- **CocoIndex Support**: Advanced processing workflows
- **Comprehensive Documentation**: Detailed guides and examples
- **Testing Suite**: Complete test coverage
- **Demo Scripts**: Interactive demonstrations

### When to Use Full Version:
- Integration with existing Pinecone infrastructure
- Advanced hybrid search capabilities
- CocoIndex framework workflows
- Development and testing

### When to Use Minimal Version:
- ✅ **Simple standalone operation** (recommended for most users)
- ✅ **Clean, focused codebase**
- ✅ **Minimal dependencies**
- ✅ **Easy deployment**

## Support

For issues and questions:
1. Check the logs with `--verbose` flag
2. Verify all dependencies are installed
3. Test with sample documents first
4. Check Neo4j and LLM API connectivity
5. Use `python test_config.py` to verify setup
