"""
Entity Discovery Module for Enterprise KG Minimal

This module provides advanced entity discovery capabilities for the hybrid search system.
It extracts entities from search results, analyzes their relationships, and provides
intelligent entity linking and disambiguation.
"""

import logging
import re
from typing import List, Dict, Any, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict, Counter

from constants.entities import EntityType
from constants.relationships import RelationshipType


logger = logging.getLogger(__name__)


class EntityConfidence(Enum):
    """Entity confidence levels."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class DiscoveredEntity:
    """Represents a discovered entity with metadata."""
    name: str
    entity_type: str
    confidence: float
    source_chunks: List[str]
    context_snippets: List[str]
    aliases: Set[str]
    properties: Dict[str, Any]
    relationships: List[Dict[str, Any]]


@dataclass
class EntityCluster:
    """Represents a cluster of related entities."""
    primary_entity: DiscoveredEntity
    related_entities: List[DiscoveredEntity]
    cluster_confidence: float
    relationship_types: Set[str]
    common_context: str


class EntityDiscoveryEngine:
    """
    Advanced entity discovery engine for extracting and analyzing entities
    from search results and document chunks.
    
    Features:
    - Named entity recognition from text
    - Entity type classification
    - Entity linking and disambiguation
    - Relationship discovery
    - Entity clustering
    """
    
    def __init__(self):
        """Initialize the entity discovery engine."""
        self.entity_patterns = self._initialize_entity_patterns()
        self.type_indicators = self._initialize_type_indicators()
        self.relationship_indicators = self._initialize_relationship_indicators()
        self.stop_words = self._initialize_stop_words()
    
    def discover_entities(
        self, 
        search_results: List[Dict[str, Any]], 
        min_confidence: float = 0.3
    ) -> List[DiscoveredEntity]:
        """
        Discover entities from search results.
        
        Args:
            search_results: List of search results with text content
            min_confidence: Minimum confidence threshold for entities
            
        Returns:
            List of discovered entities
        """
        logger.info(f"Discovering entities from {len(search_results)} search results")
        
        # Extract raw entities from all chunks
        raw_entities = []
        for result in search_results:
            chunk_text = self._extract_text_from_result(result)
            chunk_entities = self._extract_entities_from_text(chunk_text, result)
            raw_entities.extend(chunk_entities)
        
        # Group and merge similar entities
        merged_entities = self._merge_similar_entities(raw_entities)
        
        # Calculate confidence scores
        scored_entities = self._calculate_entity_confidence(merged_entities, search_results)
        
        # Filter by confidence threshold
        filtered_entities = [e for e in scored_entities if e.confidence >= min_confidence]
        
        # Sort by confidence
        filtered_entities.sort(key=lambda x: x.confidence, reverse=True)
        
        logger.info(f"Discovered {len(filtered_entities)} entities above confidence threshold")
        return filtered_entities
    
    def discover_entity_relationships(
        self, 
        entities: List[DiscoveredEntity], 
        search_results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Discover relationships between entities.
        
        Args:
            entities: List of discovered entities
            search_results: Original search results for context
            
        Returns:
            List of discovered relationships
        """
        logger.info(f"Discovering relationships between {len(entities)} entities")
        
        relationships = []
        entity_names = {e.name.lower(): e for e in entities}
        
        for result in search_results:
            chunk_text = self._extract_text_from_result(result)
            chunk_relationships = self._extract_relationships_from_text(chunk_text, entity_names)
            relationships.extend(chunk_relationships)
        
        # Deduplicate and score relationships
        unique_relationships = self._deduplicate_relationships(relationships)
        
        logger.info(f"Discovered {len(unique_relationships)} unique relationships")
        return unique_relationships
    
    def cluster_entities(
        self, 
        entities: List[DiscoveredEntity], 
        max_clusters: int = 10
    ) -> List[EntityCluster]:
        """
        Cluster related entities together.
        
        Args:
            entities: List of discovered entities
            max_clusters: Maximum number of clusters to create
            
        Returns:
            List of entity clusters
        """
        logger.info(f"Clustering {len(entities)} entities into max {max_clusters} clusters")
        
        # Simple clustering based on shared context and relationships
        clusters = []
        used_entities = set()
        
        for entity in entities:
            if entity.name in used_entities:
                continue
            
            # Find related entities
            related = self._find_related_entities(entity, entities, used_entities)
            
            if related:
                cluster = EntityCluster(
                    primary_entity=entity,
                    related_entities=related,
                    cluster_confidence=self._calculate_cluster_confidence(entity, related),
                    relationship_types=self._extract_cluster_relationships(entity, related),
                    common_context=self._extract_common_context(entity, related)
                )
                clusters.append(cluster)
                
                # Mark entities as used
                used_entities.add(entity.name)
                for rel_entity in related:
                    used_entities.add(rel_entity.name)
        
        # Sort clusters by confidence
        clusters.sort(key=lambda x: x.cluster_confidence, reverse=True)
        
        logger.info(f"Created {len(clusters)} entity clusters")
        return clusters[:max_clusters]
    
    def _initialize_entity_patterns(self) -> Dict[str, List[str]]:
        """Initialize regex patterns for entity recognition."""
        return {
            "person": [
                r"\b[A-Z][a-z]+ [A-Z][a-z]+\b",  # First Last
                r"\b(?:Mr|Ms|Dr|Prof)\.?\s+[A-Z][a-z]+ [A-Z][a-z]+\b",  # Title + Name
                r"\b[A-Z][a-z]+(?:\s+[A-Z]\.?)?\s+[A-Z][a-z]+\b"  # First M. Last
            ],
            "organization": [
                r"\b[A-Z][a-zA-Z\s&]+ (?:Inc|Corp|LLC|Ltd|Company|Corporation)\b",
                r"\b[A-Z][a-zA-Z\s]+ (?:Department|Division|Team|Group)\b",
                r"\b(?:Department|Division|Team|Group) of [A-Z][a-zA-Z\s]+\b"
            ],
            "project": [
                r"\bProject [A-Z][a-zA-Z0-9\s]+\b",
                r"\b[A-Z][a-zA-Z0-9\s]+ (?:Project|Initiative|Program)\b",
                r"\b(?:Operation|Mission|Campaign) [A-Z][a-zA-Z0-9\s]+\b"
            ],
            "system": [
                r"\b[A-Z][a-zA-Z0-9\s]+ (?:System|Platform|Application|Software|Tool)\b",
                r"\b(?:CRM|ERP|API|UI|UX|ML|AI|DB) [A-Z][a-zA-Z0-9\s]*\b",
                r"\b[A-Z][a-zA-Z0-9]+ (?:Database|Server|Service)\b"
            ],
            "location": [
                r"\b[A-Z][a-zA-Z\s]+ (?:Office|Building|Campus|Center)\b",
                r"\b(?:Room|Floor|Suite) [A-Z0-9][a-zA-Z0-9\s]*\b"
            ]
        }
    
    def _initialize_type_indicators(self) -> Dict[str, List[str]]:
        """Initialize indicators for entity type classification."""
        return {
            "Person": ["manager", "developer", "analyst", "engineer", "director", "lead", "specialist"],
            "Project": ["project", "initiative", "program", "campaign", "effort", "development"],
            "System": ["system", "platform", "application", "software", "tool", "database", "api"],
            "Organization": ["department", "team", "division", "group", "company", "corporation"],
            "Document": ["document", "report", "specification", "manual", "guide", "policy"],
            "Location": ["office", "building", "room", "floor", "campus", "center", "facility"]
        }
    
    def _initialize_relationship_indicators(self) -> Dict[str, List[str]]:
        """Initialize indicators for relationship detection."""
        return {
            "MANAGES": ["manages", "leads", "supervises", "oversees", "heads"],
            "WORKS_FOR": ["works for", "employed by", "reports to", "under"],
            "INVOLVED_IN": ["involved in", "participates in", "works on", "assigned to"],
            "USES": ["uses", "utilizes", "operates", "accesses", "employs"],
            "INTEGRATES_WITH": ["integrates with", "connects to", "interfaces with", "links to"],
            "COLLABORATES_WITH": ["collaborates with", "works with", "partners with", "teams up with"],
            "LOCATED_IN": ["located in", "based in", "situated in", "housed in"]
        }
    
    def _initialize_stop_words(self) -> Set[str]:
        """Initialize stop words to filter out."""
        return {
            "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by",
            "this", "that", "these", "those", "a", "an", "is", "are", "was", "were",
            "will", "would", "could", "should", "may", "might", "can", "must"
        }
    
    def _extract_text_from_result(self, result: Dict[str, Any]) -> str:
        """Extract text content from a search result."""
        # Handle different result formats
        if "metadata" in result and "chunk_text" in result["metadata"]:
            return result["metadata"]["chunk_text"]
        elif "text" in result:
            return result["text"]
        elif "content" in result:
            return result["content"]
        else:
            return str(result)
    
    def _extract_entities_from_text(self, text: str, source_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract entities from a text chunk."""
        entities = []
        
        for entity_type, patterns in self.entity_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text)
                for match in matches:
                    entity_name = match.group().strip()
                    
                    # Skip if it's a stop word or too short
                    if entity_name.lower() in self.stop_words or len(entity_name) < 3:
                        continue
                    
                    # Extract context around the entity
                    start = max(0, match.start() - 50)
                    end = min(len(text), match.end() + 50)
                    context = text[start:end].strip()
                    
                    entities.append({
                        "name": entity_name,
                        "type": entity_type,
                        "context": context,
                        "source": source_result,
                        "position": (match.start(), match.end())
                    })
        
        return entities

    def _merge_similar_entities(self, raw_entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Merge similar entities that likely refer to the same thing."""
        entity_groups = defaultdict(list)

        # Group entities by normalized name
        for entity in raw_entities:
            normalized_name = self._normalize_entity_name(entity["name"])
            entity_groups[normalized_name].append(entity)

        merged_entities = []
        for normalized_name, group in entity_groups.items():
            if not group:
                continue

            # Use the most complete name as the primary name
            primary_entity = max(group, key=lambda x: len(x["name"]))

            # Collect all contexts and sources
            all_contexts = [e["context"] for e in group]
            all_sources = [e["source"] for e in group]

            # Collect aliases
            aliases = set(e["name"] for e in group if e["name"] != primary_entity["name"])

            merged_entity = {
                "name": primary_entity["name"],
                "type": primary_entity["type"],
                "contexts": all_contexts,
                "sources": all_sources,
                "aliases": aliases,
                "frequency": len(group)
            }

            merged_entities.append(merged_entity)

        return merged_entities

    def _normalize_entity_name(self, name: str) -> str:
        """Normalize entity name for comparison."""
        # Remove common prefixes/suffixes and normalize case
        normalized = name.strip().lower()

        # Remove common titles
        prefixes = ["mr.", "ms.", "dr.", "prof.", "the "]
        for prefix in prefixes:
            if normalized.startswith(prefix):
                normalized = normalized[len(prefix):].strip()

        # Remove common suffixes
        suffixes = [" inc", " corp", " llc", " ltd", " company", " system", " platform"]
        for suffix in suffixes:
            if normalized.endswith(suffix):
                normalized = normalized[:-len(suffix)].strip()

        return normalized

    def _calculate_entity_confidence(
        self,
        merged_entities: List[Dict[str, Any]],
        search_results: List[Dict[str, Any]]
    ) -> List[DiscoveredEntity]:
        """Calculate confidence scores for entities."""
        discovered_entities = []

        for entity_data in merged_entities:
            # Base confidence on frequency and context quality
            frequency_score = min(1.0, entity_data["frequency"] / 5.0)  # Max at 5 occurrences

            # Context quality score based on surrounding text
            context_score = self._calculate_context_quality(entity_data["contexts"])

            # Type consistency score
            type_score = self._calculate_type_consistency(entity_data["name"], entity_data["type"])

            # Overall confidence
            confidence = (frequency_score * 0.4 + context_score * 0.4 + type_score * 0.2)

            # Extract properties from contexts
            properties = self._extract_entity_properties(entity_data["contexts"], entity_data["type"])

            discovered_entity = DiscoveredEntity(
                name=entity_data["name"],
                entity_type=entity_data["type"].title(),
                confidence=confidence,
                source_chunks=[self._extract_text_from_result(src) for src in entity_data["sources"]],
                context_snippets=entity_data["contexts"][:5],  # Limit to top 5
                aliases=entity_data["aliases"],
                properties=properties,
                relationships=[]  # Will be populated later
            )

            discovered_entities.append(discovered_entity)

        return discovered_entities

    def _calculate_context_quality(self, contexts: List[str]) -> float:
        """Calculate quality score based on context richness."""
        if not contexts:
            return 0.0

        # Score based on context length and informativeness
        total_score = 0.0
        for context in contexts:
            # Longer contexts are generally better
            length_score = min(1.0, len(context) / 100.0)

            # Contexts with more informative words are better
            informative_words = ["manages", "develops", "leads", "works", "responsible", "involved"]
            info_score = sum(1 for word in informative_words if word in context.lower()) / len(informative_words)

            total_score += (length_score * 0.6 + info_score * 0.4)

        return total_score / len(contexts)

    def _calculate_type_consistency(self, entity_name: str, entity_type: str) -> float:
        """Calculate how well the entity name matches its type."""
        name_lower = entity_name.lower()

        if entity_type not in self.type_indicators:
            return 0.5  # Neutral score for unknown types

        indicators = self.type_indicators[entity_type.title()]
        matches = sum(1 for indicator in indicators if indicator in name_lower)

        return min(1.0, matches / 2.0)  # Max score at 2 matches

    def _extract_entity_properties(self, contexts: List[str], entity_type: str) -> Dict[str, Any]:
        """Extract properties for an entity from its contexts."""
        properties = {}

        # Extract common properties based on type
        if entity_type.lower() == "person":
            properties.update(self._extract_person_properties(contexts))
        elif entity_type.lower() == "project":
            properties.update(self._extract_project_properties(contexts))
        elif entity_type.lower() == "system":
            properties.update(self._extract_system_properties(contexts))

        return properties

    def _extract_person_properties(self, contexts: List[str]) -> Dict[str, Any]:
        """Extract person-specific properties."""
        properties = {}

        # Extract roles/titles
        role_patterns = [
            r"(?:is|works as|serves as)\s+(?:a|an|the)?\s*([a-zA-Z\s]+?)(?:\s+(?:at|for|in)|$)",
            r"([A-Z][a-zA-Z\s]+?)\s+(?:manager|director|lead|engineer|analyst|developer)"
        ]

        roles = set()
        for context in contexts:
            for pattern in role_patterns:
                matches = re.findall(pattern, context, re.IGNORECASE)
                roles.update(match.strip() for match in matches if len(match.strip()) > 2)

        if roles:
            properties["roles"] = list(roles)

        return properties

    def _extract_project_properties(self, contexts: List[str]) -> Dict[str, Any]:
        """Extract project-specific properties."""
        properties = {}

        # Extract status indicators
        status_patterns = [
            r"project\s+(?:is|was)\s+([a-zA-Z\s]+?)(?:\s|$)",
            r"status\s*:\s*([a-zA-Z\s]+?)(?:\s|$)"
        ]

        statuses = set()
        for context in contexts:
            for pattern in status_patterns:
                matches = re.findall(pattern, context, re.IGNORECASE)
                statuses.update(match.strip() for match in matches if len(match.strip()) > 2)

        if statuses:
            properties["status"] = list(statuses)

        return properties

    def _extract_system_properties(self, contexts: List[str]) -> Dict[str, Any]:
        """Extract system-specific properties."""
        properties = {}

        # Extract technology stack
        tech_patterns = [
            r"(?:built|developed|using|with)\s+([A-Z][a-zA-Z0-9\s]+?)(?:\s+(?:and|,)|$)",
            r"(?:technology|platform|framework)\s*:\s*([a-zA-Z0-9\s,]+?)(?:\s|$)"
        ]

        technologies = set()
        for context in contexts:
            for pattern in tech_patterns:
                matches = re.findall(pattern, context, re.IGNORECASE)
                technologies.update(match.strip() for match in matches if len(match.strip()) > 2)

        if technologies:
            properties["technologies"] = list(technologies)

        return properties

    def _extract_relationships_from_text(
        self,
        text: str,
        entity_names: Dict[str, DiscoveredEntity]
    ) -> List[Dict[str, Any]]:
        """Extract relationships from text given known entities."""
        relationships = []

        for rel_type, indicators in self.relationship_indicators.items():
            for indicator in indicators:
                # Find sentences containing the relationship indicator
                sentences = re.split(r'[.!?]+', text)
                for sentence in sentences:
                    if indicator.lower() in sentence.lower():
                        # Find entities in this sentence
                        sentence_entities = []
                        for entity_name, entity in entity_names.items():
                            if entity_name in sentence.lower():
                                sentence_entities.append(entity)

                        # Create relationships between entities in the sentence
                        if len(sentence_entities) >= 2:
                            for i, source in enumerate(sentence_entities):
                                for target in sentence_entities[i+1:]:
                                    relationships.append({
                                        "source": source.name,
                                        "target": target.name,
                                        "relationship": rel_type,
                                        "context": sentence.strip(),
                                        "confidence": 0.7  # Base confidence
                                    })

        return relationships

    def _deduplicate_relationships(self, relationships: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate relationships and merge similar ones."""
        unique_rels = {}

        for rel in relationships:
            # Create a key for deduplication
            key = (rel["source"].lower(), rel["target"].lower(), rel["relationship"])

            if key not in unique_rels:
                unique_rels[key] = rel
            else:
                # Merge contexts and update confidence
                existing = unique_rels[key]
                existing["confidence"] = max(existing["confidence"], rel["confidence"])
                if rel["context"] not in existing["context"]:
                    existing["context"] += " | " + rel["context"]

        return list(unique_rels.values())

    def _find_related_entities(
        self,
        primary_entity: DiscoveredEntity,
        all_entities: List[DiscoveredEntity],
        used_entities: Set[str]
    ) -> List[DiscoveredEntity]:
        """Find entities related to the primary entity."""
        related = []

        for entity in all_entities:
            if entity.name in used_entities or entity.name == primary_entity.name:
                continue

            # Check for shared contexts
            shared_contexts = set(primary_entity.context_snippets) & set(entity.context_snippets)
            if shared_contexts:
                related.append(entity)
                continue

            # Check for name similarity (potential aliases)
            if self._are_names_similar(primary_entity.name, entity.name):
                related.append(entity)
                continue

            # Check for type compatibility and context overlap
            if (primary_entity.entity_type == entity.entity_type and
                self._have_context_overlap(primary_entity, entity)):
                related.append(entity)

        return related[:5]  # Limit to top 5 related entities

    def _are_names_similar(self, name1: str, name2: str) -> bool:
        """Check if two names are similar (potential aliases)."""
        norm1 = self._normalize_entity_name(name1)
        norm2 = self._normalize_entity_name(name2)

        # Check for substring relationships
        if norm1 in norm2 or norm2 in norm1:
            return True

        # Check for word overlap
        words1 = set(norm1.split())
        words2 = set(norm2.split())
        overlap = len(words1 & words2)

        return overlap >= min(len(words1), len(words2)) * 0.5

    def _have_context_overlap(self, entity1: DiscoveredEntity, entity2: DiscoveredEntity) -> bool:
        """Check if two entities have overlapping contexts."""
        contexts1 = " ".join(entity1.context_snippets).lower()
        contexts2 = " ".join(entity2.context_snippets).lower()

        # Simple word overlap check
        words1 = set(contexts1.split())
        words2 = set(contexts2.split())
        overlap = len(words1 & words2)

        return overlap >= 3  # At least 3 shared words

    def _calculate_cluster_confidence(
        self,
        primary: DiscoveredEntity,
        related: List[DiscoveredEntity]
    ) -> float:
        """Calculate confidence for an entity cluster."""
        if not related:
            return primary.confidence

        # Average confidence of all entities in cluster
        total_confidence = primary.confidence + sum(e.confidence for e in related)
        avg_confidence = total_confidence / (len(related) + 1)

        # Boost for cluster size (more entities = more confidence)
        size_boost = min(0.2, len(related) * 0.05)

        return min(1.0, avg_confidence + size_boost)

    def _extract_cluster_relationships(
        self,
        primary: DiscoveredEntity,
        related: List[DiscoveredEntity]
    ) -> Set[str]:
        """Extract relationship types within a cluster."""
        relationships = set()

        # Extract from primary entity relationships
        for rel in primary.relationships:
            relationships.add(rel.get("relationship", "RELATED_TO"))

        # Extract from related entities
        for entity in related:
            for rel in entity.relationships:
                relationships.add(rel.get("relationship", "RELATED_TO"))

        return relationships

    def _extract_common_context(
        self,
        primary: DiscoveredEntity,
        related: List[DiscoveredEntity]
    ) -> str:
        """Extract common context themes from a cluster."""
        all_contexts = primary.context_snippets[:]
        for entity in related:
            all_contexts.extend(entity.context_snippets)

        # Find most common words (simple approach)
        all_text = " ".join(all_contexts).lower()
        words = re.findall(r'\b\w+\b', all_text)
        word_counts = Counter(words)

        # Filter out stop words and get top words
        meaningful_words = [
            word for word, count in word_counts.most_common(10)
            if word not in self.stop_words and len(word) > 3
        ]

        return " ".join(meaningful_words[:5])


def create_entity_discovery_engine() -> EntityDiscoveryEngine:
    """Create an entity discovery engine with default configuration."""
    return EntityDiscoveryEngine()
