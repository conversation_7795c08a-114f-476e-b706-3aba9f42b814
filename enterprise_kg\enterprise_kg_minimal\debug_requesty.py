#!/usr/bin/env python3
"""
Debug script to test Requesty API responses and understand the format.
"""

import os
import sys
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from standalone_processor import LLMClient

def test_requesty_response():
    """Test what Requesty actually returns."""
    print("🔍 Debugging Requesty API Response Format")
    print("=" * 50)
    
    api_key = os.getenv("REQUESTY_API_KEY")
    if not api_key:
        print("✗ No Requesty API key found")
        return
    
    # Test models that gave 200 responses
    working_models = [
        "anthropic/claude-3-5-sonnet-20241022",
        "anthropic/claude-3-5-haiku-20241022",
        "openai/gpt-4o",
        "openai/gpt-4o-mini"
    ]
    
    for model in working_models:
        print(f"\n🧪 Testing model: {model}")
        print("-" * 40)
        
        try:
            client = LLMClient(provider="requesty", model=model, api_key=api_key)
            
            # Test 1: Structured response
            print("Test 1: Structured response")
            prompt = "Extract one relationship from: <PERSON> works at Company"
            schema = '{"relationships": [{"subject": "string", "predicate": "string", "object": "string"}]}'

            structured_response = client.generate_structured_response(prompt, schema)
            print(f"Structured response type: {type(structured_response)}")
            print(f"Structured response: {structured_response}")

            # Test 2: Raw API call to see actual response
            print("\nTest 2: Raw API response inspection")
            try:
                # Access the underlying client if possible
                if hasattr(client, 'client'):
                    raw_response = client.client.chat.completions.create(
                        model=model,
                        messages=[
                            {"role": "system", "content": "You are a helpful assistant that extracts relationships."},
                            {"role": "user", "content": "Extract one relationship from: John works at Company. Respond in JSON format: {\"relationships\": [{\"subject\": \"string\", \"predicate\": \"string\", \"object\": \"string\"}]}"}
                        ],
                        max_tokens=200,
                        temperature=0.1
                    )
                    print(f"Raw response type: {type(raw_response)}")
                    print(f"Raw response: {raw_response}")
                    
                    if hasattr(raw_response, 'choices') and raw_response.choices:
                        content = raw_response.choices[0].message.content
                        print(f"Content: {content}")
                        
                        # Try to parse as JSON
                        try:
                            parsed = json.loads(content)
                            print(f"Parsed JSON: {parsed}")
                        except json.JSONDecodeError as e:
                            print(f"JSON parse error: {e}")
                            print("Content is not valid JSON")
                
            except Exception as e:
                print(f"Raw API test failed: {e}")
            
            # If we got this far, this model works
            print(f"✅ Model {model} is working!")
            break
            
        except Exception as e:
            print(f"❌ Model {model} failed: {e}")
            continue
    
    print("\n" + "=" * 50)
    print("Debug complete!")

if __name__ == "__main__":
    test_requesty_response()
