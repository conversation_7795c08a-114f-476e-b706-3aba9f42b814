#!/usr/bin/env python3
"""
Integration Example: How to integrate Hybrid Search into existing projects

This script demonstrates how to integrate the hybrid search engine into
your existing enterprise applications and workflows.

Key Integration Patterns:
1. API Endpoint Integration
2. Batch Processing Integration  
3. Real-time Search Integration
4. Custom Workflow Integration

Usage:
    python integration_example.py
"""

import os
import sys
import asyncio
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from hybrid_search_engine import (
    HybridSearchEngine, 
    SearchMethod, 
    SemanticSearchClient,
    HybridSearchResponse
)
from storage.neo4j_client import Neo4jClient, Neo4jConnection

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class SearchRequest:
    """Represents a search request from your application."""
    query: str
    user_id: str
    org_id: str
    context: Dict[str, Any]
    timestamp: datetime


@dataclass
class SearchResponse:
    """Represents a search response to your application."""
    request_id: str
    query: str
    answer: str
    sources: List[str]
    confidence: float
    processing_time_ms: int
    metadata: Dict[str, Any]


class EnterpriseSearchService:
    """
    Enterprise Search Service that wraps the hybrid search engine
    for integration into existing applications.
    
    This service provides:
    - Request/response handling
    - User context management
    - Caching capabilities
    - Audit logging
    - Error handling
    """
    
    def __init__(self, hybrid_engine: HybridSearchEngine):
        """Initialize the enterprise search service."""
        self.hybrid_engine = hybrid_engine
        self.request_cache = {}  # Simple in-memory cache
        self.audit_log = []
        
    async def search(self, request: SearchRequest) -> SearchResponse:
        """
        Process a search request and return a formatted response.
        
        Args:
            request: Search request with user context
            
        Returns:
            Formatted search response
        """
        start_time = datetime.now()
        request_id = f"req_{int(start_time.timestamp() * 1000)}"
        
        logger.info(f"Processing search request {request_id}: '{request.query}' for user {request.user_id}")
        
        try:
            # Check cache first (optional optimization)
            cache_key = f"{request.query}_{request.org_id}"
            if cache_key in self.request_cache:
                logger.info(f"Cache hit for request {request_id}")
                cached_response = self.request_cache[cache_key]
                return self._create_response(request_id, request, cached_response, start_time, from_cache=True)
            
            # Perform hybrid search
            hybrid_response = await self.hybrid_engine.search(
                query=request.query,
                method=SearchMethod.AUTO_HYBRID,
                top_k_semantic=5,
                top_k_graph=10,
                org_id=request.org_id
            )
            
            # Cache the response (with TTL in real implementation)
            self.request_cache[cache_key] = hybrid_response
            
            # Create formatted response
            response = self._create_response(request_id, request, hybrid_response, start_time)
            
            # Log for audit
            self._log_search_audit(request, response)
            
            return response
            
        except Exception as e:
            logger.error(f"Search failed for request {request_id}: {e}")
            
            # Return error response
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            return SearchResponse(
                request_id=request_id,
                query=request.query,
                answer=f"Search failed: {str(e)}",
                sources=[],
                confidence=0.0,
                processing_time_ms=processing_time,
                metadata={"error": str(e), "user_id": request.user_id}
            )
    
    def _create_response(
        self, 
        request_id: str, 
        request: SearchRequest, 
        hybrid_response: HybridSearchResponse, 
        start_time: datetime,
        from_cache: bool = False
    ) -> SearchResponse:
        """Create a formatted search response."""
        processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
        
        return SearchResponse(
            request_id=request_id,
            query=request.query,
            answer=hybrid_response.answer,
            sources=hybrid_response.source_files,
            confidence=hybrid_response.confidence,
            processing_time_ms=processing_time,
            metadata={
                "user_id": request.user_id,
                "org_id": request.org_id,
                "method": hybrid_response.method,
                "semantic_chunks": hybrid_response.semantic_search.get("total_chunks", 0),
                "graph_relationships": hybrid_response.knowledge_graph.get("total_relationships", 0),
                "from_cache": from_cache,
                "processing_info": hybrid_response.processing_info
            }
        )
    
    def _log_search_audit(self, request: SearchRequest, response: SearchResponse):
        """Log search activity for audit purposes."""
        audit_entry = {
            "timestamp": datetime.now().isoformat(),
            "request_id": response.request_id,
            "user_id": request.user_id,
            "org_id": request.org_id,
            "query": request.query,
            "confidence": response.confidence,
            "processing_time_ms": response.processing_time_ms,
            "sources_count": len(response.sources)
        }
        
        self.audit_log.append(audit_entry)
        logger.info(f"Audit logged for request {response.request_id}")
    
    def get_search_analytics(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """Get search analytics for monitoring and optimization."""
        filtered_logs = self.audit_log
        if user_id:
            filtered_logs = [log for log in self.audit_log if log["user_id"] == user_id]
        
        if not filtered_logs:
            return {"total_searches": 0}
        
        total_searches = len(filtered_logs)
        avg_processing_time = sum(log["processing_time_ms"] for log in filtered_logs) / total_searches
        avg_confidence = sum(log["confidence"] for log in filtered_logs) / total_searches
        
        return {
            "total_searches": total_searches,
            "avg_processing_time_ms": avg_processing_time,
            "avg_confidence": avg_confidence,
            "unique_users": len(set(log["user_id"] for log in filtered_logs)),
            "cache_hit_rate": len([log for log in filtered_logs if log.get("from_cache")]) / total_searches
        }


# Integration Pattern Examples

async def api_endpoint_example():
    """Example: How to integrate into a REST API endpoint."""
    print("\n🌐 API Endpoint Integration Example")
    print("-" * 50)
    
    # This would typically be in your FastAPI/Flask route handler
    async def search_endpoint(query: str, user_id: str, org_id: str = "rapid_innovation"):
        """Simulated API endpoint."""
        
        # Create search request
        request = SearchRequest(
            query=query,
            user_id=user_id,
            org_id=org_id,
            context={"source": "api", "version": "v1"},
            timestamp=datetime.now()
        )
        
        # Process search
        response = await search_service.search(request)
        
        # Return API response (would be JSON in real API)
        return {
            "request_id": response.request_id,
            "query": response.query,
            "answer": response.answer,
            "sources": response.sources,
            "confidence": response.confidence,
            "processing_time_ms": response.processing_time_ms
        }
    
    # Simulate API calls
    api_response = await search_endpoint("Who works on Project Alpha?", "user123")
    print(f"API Response: {api_response}")


async def batch_processing_example():
    """Example: How to integrate into batch processing workflows."""
    print("\n📊 Batch Processing Integration Example")
    print("-" * 50)
    
    # Simulate batch of queries from different sources
    batch_queries = [
        ("What systems does Sarah Smith use?", "user456"),
        ("How are CRM and Analytics connected?", "user789"),
        ("Tell me about company culture", "user123")
    ]
    
    results = []
    for query, user_id in batch_queries:
        request = SearchRequest(
            query=query,
            user_id=user_id,
            org_id="rapid_innovation",
            context={"source": "batch", "batch_id": "batch_001"},
            timestamp=datetime.now()
        )
        
        response = await search_service.search(request)
        results.append({
            "query": query,
            "user_id": user_id,
            "confidence": response.confidence,
            "processing_time_ms": response.processing_time_ms
        })
    
    print(f"Batch processed {len(results)} queries")
    for result in results:
        print(f"  - '{result['query']}' -> confidence: {result['confidence']:.2f}")


async def real_time_integration_example():
    """Example: How to integrate into real-time applications."""
    print("\n⚡ Real-time Integration Example")
    print("-" * 50)
    
    # Simulate real-time search requests
    real_time_queries = [
        "Who manages the development team?",
        "What projects is Mike Johnson involved in?",
        "Show me system integrations"
    ]
    
    for i, query in enumerate(real_time_queries, 1):
        print(f"\nReal-time query {i}: {query}")
        
        request = SearchRequest(
            query=query,
            user_id="realtime_user",
            org_id="rapid_innovation",
            context={"source": "realtime", "session_id": "session_001"},
            timestamp=datetime.now()
        )
        
        start_time = datetime.now()
        response = await search_service.search(request)
        end_time = datetime.now()
        
        print(f"  Response time: {response.processing_time_ms}ms")
        print(f"  Confidence: {response.confidence:.2f}")
        print(f"  Answer: {response.answer[:100]}...")


async def main():
    """Main integration demonstration."""
    global search_service
    
    print("🔧 Enterprise Hybrid Search Integration Examples")
    print("="*60)
    
    # Step 1: Initialize hybrid search engine
    print("🚀 Initializing hybrid search engine...")
    try:
        neo4j_conn = Neo4jConnection(
            uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            user=os.getenv("NEO4J_USER", "neo4j"),
            password=os.getenv("NEO4J_PASSWORD", "password")
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        # Create enhanced semantic client (with mock data)
        from test_hybrid_search import EnhancedSemanticSearchClient
        semantic_client = EnhancedSemanticSearchClient()
        
        # Create hybrid search engine
        hybrid_engine = HybridSearchEngine(
            neo4j_client=neo4j_client,
            semantic_client=semantic_client,
            default_method=SearchMethod.AUTO_HYBRID
        )
        
        # Create enterprise search service
        search_service = EnterpriseSearchService(hybrid_engine)
        
        print("✓ Hybrid search engine initialized")
        
    except Exception as e:
        print(f"✗ Failed to initialize: {e}")
        return 1
    
    # Step 2: Run integration examples
    try:
        await api_endpoint_example()
        await batch_processing_example()
        await real_time_integration_example()
        
        # Step 3: Show analytics
        print("\n📈 Search Analytics")
        print("-" * 50)
        analytics = search_service.get_search_analytics()
        for key, value in analytics.items():
            print(f"  {key}: {value}")
        
    except Exception as e:
        logger.error(f"Integration examples failed: {e}")
        return 1
    finally:
        neo4j_client.close()
    
    print("\n✅ Integration examples completed successfully!")
    
    # Step 4: Show integration recommendations
    print("\n💡 Integration Recommendations")
    print("-" * 50)
    print("1. API Integration: Use FastAPI/Flask with async endpoints")
    print("2. Caching: Implement Redis for production caching")
    print("3. Monitoring: Add Prometheus metrics for performance tracking")
    print("4. Security: Implement proper authentication and authorization")
    print("5. Scaling: Use connection pooling for Neo4j and Pinecone")
    print("6. Error Handling: Implement circuit breakers for resilience")
    
    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
