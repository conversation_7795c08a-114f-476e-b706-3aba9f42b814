#!/usr/bin/env python3
"""
Enhanced Enterprise KG Test with File Output

This script tests all enhanced features and saves the graph to files
instead of Neo4j, allowing us to see the complete pipeline working
and visualize the connected graph structure.
"""

import os
import sys
import json
import logging
from typing import List, Dict, Any, Set
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import enhanced components
from chunking_engine import ChunkingEngine, ChunkingStrategy, create_chunking_engine
from standalone_processor import LLMClient
from constants.schemas import EntityRelationship
from prompt_generator import PromptGenerator, create_full_prompt_generator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedKGWithFileOutput:
    """Enhanced KG processor that saves results to files instead of Neo4j."""
    
    def __init__(self):
        """Initialize the enhanced processor."""
        self.documents_dir = "documents"
        self.output_dir = "graph_output"
        self.test_documents = [
            "Culture Values - Rapid.pdf",
            "JiraSource.json", 
            "Title_ Project Atlas - AI-driven Customer Insights Platform.docx"
        ]
        
        # Initialize components
        self.llm_client = None
        self.chunking_engine = None
        self.prompt_generator = None
        
        # Graph data storage
        self.all_relationships = []
        self.all_entities = {}  # entity_name -> entity_info
        self.entity_connections = {}  # entity -> set of connected entities
        self.document_stats = {}
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
    def setup_components(self) -> bool:
        """Set up enhanced components."""
        print("🔧 Setting up Enhanced Components (File Output Mode)")
        print("=" * 60)
        
        try:
            # Try Requesty first, then fallback to mock
            provider = "requesty"
            api_key = os.getenv("REQUESTY_API_KEY")

            # Try different model formats for Requesty (provider/model format)
            model_options = [
                "anthropic/claude-3-5-sonnet-20241022",
                "anthropic/claude-3-5-sonnet-20240620",
                "anthropic/claude-3-5-haiku-20241022",
                "openai/gpt-4o",
                "openai/gpt-4o-mini",
                "openai/gpt-4-turbo",
                "openai/gpt-4",
                "google/gemini-pro-1.5",
                "meta-llama/llama-3.1-70b-instruct",
                "mistral/mistral-large-2407"
            ]

            if api_key:
                print("🔄 Testing Requesty API with different model formats...")
                self.llm_client = None

                for model in model_options:
                    try:
                        print(f"  Trying model: {model}")
                        test_client = LLMClient(provider=provider, model=model, api_key=api_key)

                        # Test with a simple prompt (with timeout)
                        test_response = test_client.generate_structured_response(
                            "Extract one relationship from: John works at Company",
                            '{"relationships": [{"subject": "string", "predicate": "string", "object": "string"}]}'
                        )

                        # Check if response is valid (can be dict with relationships or direct list)
                        if test_response:
                            if isinstance(test_response, dict) and 'relationships' in test_response:
                                relationships = test_response['relationships']
                                if isinstance(relationships, list) and len(relationships) > 0:
                                    self.llm_client = test_client
                                    print(f"✓ Requesty working with model: {model}")
                                    print(f"    Sample response: {relationships[0]}")
                                    break
                            elif isinstance(test_response, list) and len(test_response) > 0:
                                self.llm_client = test_client
                                print(f"✓ Requesty working with model: {model}")
                                print(f"    Sample response: {test_response[0]}")
                                break

                        print(f"    ⚠️  Model {model} responded but with unexpected format: {type(test_response)}")

                    except Exception as e:
                        error_msg = str(e)
                        if "400" in error_msg and "Missing provider" in error_msg:
                            print(f"    ✗ {model}: Invalid format (needs provider/model)")
                        elif "404" in error_msg and "not supported" in error_msg:
                            print(f"    ✗ {model}: Model not supported by Requesty")
                        elif "401" in error_msg or "unauthorized" in error_msg.lower():
                            print(f"    ✗ {model}: API key issue")
                        else:
                            print(f"    ✗ {model}: {error_msg[:80]}...")
                        continue

                if not self.llm_client:
                    print("⚠️  All Requesty models failed, using mock extraction mode")
            else:
                print("⚠️  No Requesty API key found, using mock extraction mode")
                self.llm_client = None
            
            # Initialize chunking engine with hybrid strategy
            self.chunking_engine = create_chunking_engine(
                strategy="hybrid",
                chunk_size=800,
                chunk_overlap=150
            )
            print("✓ Chunking Engine initialized (hybrid strategy)")
            
            # Initialize prompt generator
            self.prompt_generator = create_full_prompt_generator()
            print("✓ Prompt Generator initialized")
            
            print("✓ Output directory created:", self.output_dir)
            print()
            return True
            
        except Exception as e:
            print(f"✗ Component setup failed: {e}")
            return False
    
    def process_all_documents(self) -> bool:
        """Process all documents and extract relationships."""
        print("📄 Processing All Documents with Enhanced Pipeline")
        print("=" * 60)
        
        total_relationships = 0
        
        for doc in self.test_documents:
            doc_path = os.path.join(self.documents_dir, doc)
            print(f"\n📖 Processing: {doc}")
            
            # Step 1: Read document
            content = self._read_document_content(doc_path)
            print(f"  📊 Content length: {len(content)} characters")
            
            # Step 2: Advanced chunking
            chunks = self.chunking_engine.chunk_document(content, doc_path)
            print(f"  🔪 Created {len(chunks)} chunks using hybrid strategy")
            
            # Step 3: Entity extraction
            doc_relationships = []
            
            if self.llm_client:
                # Real LLM extraction
                for i, chunk in enumerate(chunks[:3]):  # Process first 3 chunks
                    print(f"    🤖 Processing chunk {i+1}/{min(3, len(chunks))} with LLM...")
                    
                    try:
                        prompt = self.prompt_generator.generate_relationship_extraction_prompt(chunk.text)
                        schema_description = self.prompt_generator.get_schema_description()
                        
                        response = self.llm_client.generate_structured_response(prompt, schema_description)

                        # Handle both dict and list response formats
                        relationships_data = []
                        if isinstance(response, dict) and 'relationships' in response:
                            relationships_data = response['relationships']
                        elif isinstance(response, list):
                            relationships_data = response

                        if relationships_data:
                            chunk_rels = []
                            for rel_data in relationships_data:
                                try:
                                    rel = EntityRelationship(**rel_data)
                                    chunk_rels.append(rel)
                                    doc_relationships.append(rel)
                                except Exception as e:
                                    logger.warning(f"Failed to parse relationship: {rel_data}")

                            print(f"      ✓ Extracted {len(chunk_rels)} relationships")
                        
                    except Exception as e:
                        print(f"      ⚠️  LLM extraction failed: {e}")
                        # Fall back to mock data for this chunk
                        mock_rels = self._generate_mock_relationships(chunk.text, doc)
                        doc_relationships.extend(mock_rels)
                        print(f"      📝 Using {len(mock_rels)} mock relationships")
            else:
                # Mock extraction mode
                print(f"    📝 Using mock extraction (no LLM available)")
                for i, chunk in enumerate(chunks[:2]):
                    mock_rels = self._generate_mock_relationships(chunk.text, doc)
                    doc_relationships.extend(mock_rels)
                    print(f"      ✓ Generated {len(mock_rels)} mock relationships for chunk {i+1}")
            
            # Step 4: Process relationships for this document
            self._process_document_relationships(doc_relationships, doc)
            
            print(f"  ✅ Total relationships from {doc}: {len(doc_relationships)}")
            total_relationships += len(doc_relationships)
            
            # Store document stats
            self.document_stats[doc] = {
                "content_length": len(content),
                "chunks_created": len(chunks),
                "relationships_extracted": len(doc_relationships),
                "processing_time": datetime.now().isoformat()
            }
        
        print(f"\n🎯 Total relationships extracted: {total_relationships}")
        print(f"🎯 Unique entities discovered: {len(self.all_entities)}")
        
        return total_relationships > 0
    
    def _generate_mock_relationships(self, text: str, doc_name: str) -> List[EntityRelationship]:
        """Generate mock relationships based on document content and type."""
        mock_relationships = []
        
        # Extract some basic patterns for mock data
        if "Culture Values" in doc_name:
            mock_relationships = [
                EntityRelationship(
                    subject="Rapid Automation",
                    predicate="has_value",
                    object="Innovation",
                    subject_type="Company",
                    object_type="Value",
                    confidence_score=0.9,
                    context="Company culture and values",
                    source_sentence="Rapid Automation values innovation and excellence."
                ),
                EntityRelationship(
                    subject="Rapid Automation",
                    predicate="promotes",
                    object="Collaboration",
                    subject_type="Company", 
                    object_type="Value",
                    confidence_score=0.85,
                    context="Team collaboration principles",
                    source_sentence="The company promotes collaboration among teams."
                ),
                EntityRelationship(
                    subject="Team Members",
                    predicate="follow",
                    object="Code of Conduct",
                    subject_type="Person",
                    object_type="Policy",
                    confidence_score=0.8,
                    context="Behavioral guidelines",
                    source_sentence="All team members follow the established code of conduct."
                )
            ]
        elif "Project Atlas" in doc_name:
            mock_relationships = [
                EntityRelationship(
                    subject="Project Atlas",
                    predicate="is_type_of",
                    object="AI Platform",
                    subject_type="Project",
                    object_type="Technology",
                    confidence_score=0.95,
                    context="AI-driven customer insights platform",
                    source_sentence="Project Atlas is an AI-driven customer insights platform."
                ),
                EntityRelationship(
                    subject="Project Atlas",
                    predicate="provides",
                    object="Customer Insights",
                    subject_type="Project",
                    object_type="Service",
                    confidence_score=0.9,
                    context="Platform capabilities",
                    source_sentence="The platform provides comprehensive customer insights."
                ),
                EntityRelationship(
                    subject="Data Scientists",
                    predicate="work_on",
                    object="Project Atlas",
                    subject_type="Person",
                    object_type="Project",
                    confidence_score=0.85,
                    context="Team assignment",
                    source_sentence="Data scientists are working on Project Atlas development."
                )
            ]
        elif "Jira" in doc_name:
            mock_relationships = [
                EntityRelationship(
                    subject="Development Team",
                    predicate="uses",
                    object="Jira",
                    subject_type="Team",
                    object_type="Tool",
                    confidence_score=0.9,
                    context="Project management tool usage",
                    source_sentence="The development team uses Jira for project tracking."
                ),
                EntityRelationship(
                    subject="Jira",
                    predicate="tracks",
                    object="User Stories",
                    subject_type="Tool",
                    object_type="Artifact",
                    confidence_score=0.85,
                    context="Issue tracking",
                    source_sentence="Jira tracks user stories and development tasks."
                )
            ]
        
        return mock_relationships
    
    def _process_document_relationships(self, relationships: List[EntityRelationship], doc_name: str):
        """Process relationships and build entity graph."""
        for rel in relationships:
            # Add to all relationships
            self.all_relationships.append({
                "source": rel.subject,
                "target": rel.object,
                "relationship": rel.predicate,
                "source_type": rel.subject_type,
                "target_type": rel.object_type,
                "confidence": rel.confidence_score,
                "context": rel.context,
                "source_document": doc_name,
                "source_sentence": rel.source_sentence
            })
            
            # Add entities
            self._add_entity(rel.subject, rel.subject_type, doc_name)
            self._add_entity(rel.object, rel.object_type, doc_name)
            
            # Add connections
            self._add_connection(rel.subject, rel.object)
    
    def _add_entity(self, name: str, entity_type: str, doc_name: str):
        """Add or update entity information."""
        normalized_name = name.lower().strip()
        
        if normalized_name not in self.all_entities:
            self.all_entities[normalized_name] = {
                "name": name,
                "type": entity_type or "Entity",
                "source_documents": [doc_name],
                "created_at": datetime.now().isoformat()
            }
        else:
            # Update existing entity
            if doc_name not in self.all_entities[normalized_name]["source_documents"]:
                self.all_entities[normalized_name]["source_documents"].append(doc_name)
            
            # Update type if not set
            if entity_type and self.all_entities[normalized_name]["type"] == "Entity":
                self.all_entities[normalized_name]["type"] = entity_type
    
    def _add_connection(self, entity1: str, entity2: str):
        """Add bidirectional connection between entities."""
        norm1 = entity1.lower().strip()
        norm2 = entity2.lower().strip()
        
        if norm1 not in self.entity_connections:
            self.entity_connections[norm1] = set()
        if norm2 not in self.entity_connections:
            self.entity_connections[norm2] = set()
        
        self.entity_connections[norm1].add(norm2)
        self.entity_connections[norm2].add(norm1)
    
    def save_graph_to_files(self) -> bool:
        """Save the complete graph structure to files."""
        print("💾 Saving Graph Structure to Files")
        print("=" * 60)
        
        try:
            # Save relationships
            relationships_file = os.path.join(self.output_dir, "relationships.json")
            with open(relationships_file, 'w', encoding='utf-8') as f:
                json.dump(self.all_relationships, f, indent=2, ensure_ascii=False)
            print(f"✓ Relationships saved: {relationships_file} ({len(self.all_relationships)} relationships)")
            
            # Save entities
            entities_file = os.path.join(self.output_dir, "entities.json")
            # Convert sets to lists for JSON serialization
            entities_for_json = {}
            for key, entity in self.all_entities.items():
                entities_for_json[key] = entity.copy()
            
            with open(entities_file, 'w', encoding='utf-8') as f:
                json.dump(entities_for_json, f, indent=2, ensure_ascii=False)
            print(f"✓ Entities saved: {entities_file} ({len(self.all_entities)} entities)")
            
            # Save connections
            connections_file = os.path.join(self.output_dir, "connections.json")
            connections_for_json = {k: list(v) for k, v in self.entity_connections.items()}
            with open(connections_file, 'w', encoding='utf-8') as f:
                json.dump(connections_for_json, f, indent=2, ensure_ascii=False)
            print(f"✓ Connections saved: {connections_file}")
            
            # Save document stats
            stats_file = os.path.join(self.output_dir, "document_stats.json")
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.document_stats, f, indent=2, ensure_ascii=False)
            print(f"✓ Document stats saved: {stats_file}")
            
            # Create graph visualization
            self._create_graph_visualization()
            
            return True
            
        except Exception as e:
            print(f"✗ Failed to save graph files: {e}")
            return False
    
    def _create_graph_visualization(self):
        """Create a simple text-based graph visualization."""
        viz_file = os.path.join(self.output_dir, "graph_visualization.txt")
        
        with open(viz_file, 'w', encoding='utf-8') as f:
            f.write("🕸️  ENHANCED ENTERPRISE KNOWLEDGE GRAPH\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"📊 GRAPH STATISTICS\n")
            f.write("-" * 30 + "\n")
            f.write(f"Total Entities: {len(self.all_entities)}\n")
            f.write(f"Total Relationships: {len(self.all_relationships)}\n")
            f.write(f"Documents Processed: {len(self.document_stats)}\n\n")
            
            f.write(f"👥 ENTITIES BY TYPE\n")
            f.write("-" * 30 + "\n")
            entity_types = {}
            for entity in self.all_entities.values():
                entity_type = entity["type"]
                entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            
            for entity_type, count in sorted(entity_types.items()):
                f.write(f"{entity_type}: {count}\n")
            f.write("\n")
            
            f.write(f"🔗 SAMPLE RELATIONSHIPS\n")
            f.write("-" * 30 + "\n")
            for i, rel in enumerate(self.all_relationships[:10]):
                confidence = f" (confidence: {rel['confidence']:.2f})" if rel['confidence'] else ""
                f.write(f"{i+1}. {rel['source']} → {rel['relationship']} → {rel['target']}{confidence}\n")
            
            if len(self.all_relationships) > 10:
                f.write(f"... and {len(self.all_relationships) - 10} more relationships\n")
            f.write("\n")
            
            f.write(f"🌐 ENTITY CONNECTIONS\n")
            f.write("-" * 30 + "\n")
            for entity, connections in sorted(self.entity_connections.items()):
                if connections:  # Only show connected entities
                    f.write(f"{entity} → connected to {len(connections)} entities\n")
                    for conn in sorted(list(connections)[:3]):  # Show first 3 connections
                        f.write(f"  ├─ {conn}\n")
                    if len(connections) > 3:
                        f.write(f"  └─ ... and {len(connections) - 3} more\n")
            
        print(f"✓ Graph visualization saved: {viz_file}")
    
    def _read_document_content(self, file_path: str) -> str:
        """Read document content based on file type."""
        file_extension = os.path.splitext(file_path)[1].lower()
        
        if file_extension == '.pdf':
            return self._read_pdf(file_path)
        elif file_extension == '.docx':
            return self._read_docx(file_path)
        elif file_extension == '.json':
            return self._read_json(file_path)
        else:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
    
    def _read_pdf(self, file_path: str) -> str:
        """Extract text from PDF file."""
        try:
            import pypdf
            with open(file_path, 'rb') as f:
                pdf_reader = pypdf.PdfReader(f)
                text_content = []
                for page in pdf_reader.pages:
                    text_content.append(page.extract_text())
                return '\n'.join(text_content)
        except ImportError:
            return "PDF content (pypdf not available)"
    
    def _read_docx(self, file_path: str) -> str:
        """Extract text from DOCX file."""
        try:
            from docx import Document
            doc = Document(file_path)
            text_content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            return '\n'.join(text_content)
        except ImportError:
            return "DOCX content (python-docx not available)"
    
    def _read_json(self, file_path: str) -> str:
        """Extract and format content from JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            return json.dumps(json_data, indent=2, ensure_ascii=False)
        except json.JSONDecodeError:
            return "JSON content (invalid format)"


def main():
    """Main execution function."""
    print("🚀 Enhanced Enterprise KG with File Output")
    print("=" * 70)
    print("Testing enhanced features and saving graph to files")
    print()
    
    processor = EnhancedKGWithFileOutput()
    
    # Run the complete pipeline
    tests = [
        ("Component Setup", processor.setup_components),
        ("Document Processing", processor.process_all_documents),
        ("Save Graph Files", processor.save_graph_to_files),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"Running {test_name}...")
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} failed: {e}")
            results[test_name] = False
        print()
    
    # Print summary
    print("📊 Enhanced Pipeline Test Summary")
    print("=" * 70)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status} {test_name}")
    
    print()
    print(f"Results: {passed}/{total} tests passed ({passed/total:.1%})")
    
    if passed == total:
        print("🎉 Enhanced pipeline completed successfully!")
        print(f"\n📁 Graph files saved in: {processor.output_dir}/")
        print("   - relationships.json: All extracted relationships")
        print("   - entities.json: All discovered entities")
        print("   - connections.json: Entity connection graph")
        print("   - document_stats.json: Processing statistics")
        print("   - graph_visualization.txt: Human-readable graph view")
    
    return 0 if passed == total else 1


if __name__ == "__main__":
    sys.exit(main())
