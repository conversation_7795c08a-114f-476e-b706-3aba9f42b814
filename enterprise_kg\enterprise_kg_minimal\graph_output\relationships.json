[{"source": "VISION", "target": "Table of Contents", "relationship": "contained_in", "source_type": "Document", "target_type": "Document", "confidence": 1.0, "context": "Document structure listing", "source_document": "Culture Values - Rapid.pdf", "source_sentence": "Table of Contents VISION 2"}, {"source": "MISSION", "target": "Table of Contents", "relationship": "contained_in", "source_type": "Document", "target_type": "Document", "confidence": 1.0, "context": "Document structure listing", "source_document": "Culture Values - Rapid.pdf", "source_sentence": "Table of Contents MISSION 2"}, {"source": "CORE VALUES", "target": "Table of Contents", "relationship": "contained_in", "source_type": "Document", "target_type": "Document", "confidence": 1.0, "context": "Document structure listing", "source_document": "Culture Values - Rapid.pdf", "source_sentence": "CORE VALUES 3"}, {"source": "COMPANY GOALS", "target": "Table of Contents", "relationship": "contained_in", "source_type": "Document", "target_type": "Document", "confidence": 1.0, "context": "Document structure listing", "source_document": "Culture Values - Rapid.pdf", "source_sentence": "COMPANY GOALS 4"}, {"source": "CULTURAL PILLARS", "target": "Table of Contents", "relationship": "contained_in", "source_type": "Document", "target_type": "Document", "confidence": 1.0, "context": "Document structure listing", "source_document": "Culture Values - Rapid.pdf", "source_sentence": "CULTURAL PILLARS 6"}, {"source": "Web3", "target": "blockchain", "relationship": "contains", "source_type": "Technology", "target_type": "Technology", "confidence": 0.9, "context": "Web3 pillars description", "source_document": "Culture Values - Rapid.pdf", "source_sentence": "We envision a world transformed by our expertise in the four pillars of Web3 – blockchain"}, {"source": "Web3", "target": "Mission", "relationship": "contributes_to", "source_type": "Technology", "target_type": "Objective", "confidence": 0.8, "context": "Mission statement discussing Web3 pillars", "source_document": "Culture Values - Rapid.pdf", "source_sentence": "Dedicated to harnessing the power of the four pillars of Web3, our mission is to build innovative"}, {"source": "<PERSON><PERSON>", "target": "Atlas", "relationship": "leads", "source_type": "Person", "target_type": "Project", "confidence": 1.0, "context": "Project lead information", "source_document": "JiraSource.json", "source_sentence": "lead: {display<PERSON><PERSON>: <PERSON><PERSON>}"}, {"source": "Data Ingestion", "target": "Atlas", "relationship": "part_of", "source_type": "System", "target_type": "Project", "confidence": 1.0, "context": "Project components listing", "source_document": "JiraSource.json", "source_sentence": "components: [{id: 10100, name: Data Ingestion}]"}, {"source": "AI Analysis Engine", "target": "Atlas", "relationship": "part_of", "source_type": "System", "target_type": "Project", "confidence": 1.0, "context": "Project components listing", "source_document": "JiraSource.json", "source_sentence": "components: [{id: 10101, name: AI Analysis Engine}]"}, {"source": "<PERSON><PERSON>", "target": "Project Atlas", "relationship": "owns", "source_type": "Person", "target_type": "Project", "confidence": 1.0, "context": "Project ownership designation", "source_document": "Title_ Project Atlas - AI-driven Customer Insights Platform.docx", "source_sentence": "Owner: <PERSON><PERSON> (Product Manager)"}, {"source": "Data Science", "target": "Project Atlas", "relationship": "involved_in", "source_type": "Team", "target_type": "Project", "confidence": 1.0, "context": "Teams listed as involved in project", "source_document": "Title_ Project Atlas - AI-driven Customer Insights Platform.docx", "source_sentence": "Teams Involved: - Data Science"}, {"source": "Backend Engineering", "target": "Project Atlas", "relationship": "involved_in", "source_type": "Team", "target_type": "Project", "confidence": 1.0, "context": "Teams listed as involved in project", "source_document": "Title_ Project Atlas - AI-driven Customer Insights Platform.docx", "source_sentence": "Teams Involved: - Backend Engineering"}, {"source": "Customer Success", "target": "Project Atlas", "relationship": "involved_in", "source_type": "Team", "target_type": "Project", "confidence": 1.0, "context": "Teams listed as involved in project", "source_document": "Title_ Project Atlas - AI-driven Customer Insights Platform.docx", "source_sentence": "Teams Involved: - Customer Success"}, {"source": "Project Atlas", "target": "JIRA", "relationship": "documented_in", "source_type": "Project", "target_type": "Tool", "confidence": 0.9, "context": "Project tracking in JIRA", "source_document": "Title_ Project Atlas - AI-driven Customer Insights Platform.docx", "source_sentence": "Please refer to the JIRA project 'ATLS' for current sprint updates and task assignments"}, {"source": "Project Atlas", "target": "Q2 2025", "relationship": "scheduled_for", "source_type": "Project", "target_type": "Quarter", "confidence": 1.0, "context": "Project timeline", "source_document": "Title_ Project Atlas - AI-driven Customer Insights Platform.docx", "source_sentence": "The project is scheduled to start in Q2 2025 and will run for six months"}]