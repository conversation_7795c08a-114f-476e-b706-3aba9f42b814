"""
Organizational Integration Module

This module provides optional organizational context integration for the Enterprise KG system.
It allows linking extracted knowledge to existing organizational graph structures while
maintaining backward compatibility with the standalone mode.

Usage:
    # Standalone mode (existing behavior)
    processor = create_kg_processor()
    processor.process_document(content)

    # Organizational integration mode
    processor = create_kg_processor_with_org_context()
    processor.process_document_with_org_context(
        content,
        source_file_id="file_123",
        org_context={"org_id": "techcorp", "dept_id": "engineering"}
    )
"""

from typing import Optional, Dict, List, Any
from constants.schemas import EntityRelationship, DocumentSummary
from storage.neo4j_client import Neo4jClient
from core.extractor import EntityRelationshipExtractor


class OrganizationalKGProcessor:
    """
    Enhanced KG processor with optional organizational context integration.

    This class extends the basic KG processing with:
    - File node linking
    - Organizational context inheritance
    - Context-aware search capabilities
    - Backward compatibility with standalone mode
    """

    def __init__(
        self,
        extractor: EntityRelationshipExtractor,
        neo4j_client: Neo4jClient,
        enable_org_integration: bool = False
    ):
        """
        Initialize the organizational KG processor.

        Args:
            extractor: Entity relationship extractor
            neo4j_client: Neo4j client for graph operations
            enable_org_integration: Whether to enable organizational features
        """
        self.extractor = extractor
        self.neo4j_client = neo4j_client
        self.enable_org_integration = enable_org_integration

    def process_document(
        self,
        content: str,
        source_document: Optional[str] = None
    ) -> List[EntityRelationship]:
        """
        Process document in standalone mode (existing behavior).

        Args:
            content: Document content to process
            source_document: Optional source document name

        Returns:
            List of extracted entity relationships
        """
        # Extract relationships using existing flow
        relationships = self.extractor.extract_relationships(content)

        # Store using basic method (no organizational context)
        results = []
        for rel in relationships:
            try:
                result = self.neo4j_client.create_entity_relationship(rel, source_document)
                results.append(rel)
            except Exception as e:
                print(f"Error creating relationship: {e}")

        return results

    def process_document_with_org_context(
        self,
        content: str,
        source_file_id: Optional[str] = None,
        org_context: Optional[Dict[str, str]] = None,
        source_document: Optional[str] = None
    ) -> List[EntityRelationship]:
        """
        Process document with organizational context integration.

        Args:
            content: Document content to process
            source_file_id: ID of existing file node in organizational graph
            org_context: Organizational context (org_id, dept_id, team_id)
            source_document: Optional source document name

        Returns:
            List of extracted entity relationships with organizational context
        """
        if not self.enable_org_integration:
            # Fall back to standalone mode if org integration is disabled
            return self.process_document(content, source_document)

        # Extract relationships using existing flow
        relationships = self.extractor.extract_relationships(content)

        # Enhance relationships with organizational context
        enhanced_relationships = []
        for rel in relationships:
            # Add organizational context to the relationship
            rel.source_file_id = source_file_id
            rel.org_context = org_context
            enhanced_relationships.append(rel)

        # Store using organizational context method
        results = []
        for rel in enhanced_relationships:
            try:
                result = self.neo4j_client.create_entity_relationship_with_org_context(
                    rel,
                    source_document=source_document,
                    source_file_id=source_file_id,
                    org_context=org_context
                )
                results.append(rel)
            except Exception as e:
                print(f"Error creating relationship with org context: {e}")

        return results

    def query_by_organizational_context(
        self,
        org_id: Optional[str] = None,
        dept_id: Optional[str] = None,
        team_id: Optional[str] = None,
        entity_type: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Query entities within specific organizational context.

        Args:
            org_id: Organization ID filter
            dept_id: Department ID filter
            team_id: Team ID filter
            entity_type: Entity type filter
            limit: Maximum results

        Returns:
            List of entities matching the organizational context
        """
        if not self.enable_org_integration:
            # Fall back to basic entity query
            return self.neo4j_client.query_entities(entity_type=entity_type, limit=limit)

        # Build context-aware query
        driver = self.neo4j_client._get_driver()

        with driver.session(database=self.neo4j_client.connection.database) as session:
            conditions = []
            params = {"limit": limit}

            if org_id:
                conditions.append("n.org_id = $org_id")
                params["org_id"] = org_id

            if dept_id:
                conditions.append("n.department_id = $dept_id")
                params["dept_id"] = dept_id

            if team_id:
                conditions.append("n.team_id = $team_id")
                params["team_id"] = team_id

            if entity_type:
                conditions.append(f"n:{entity_type}")

            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""

            query = f"""
            MATCH (n)
            {where_clause}
            RETURN n,
                   [(file)-[:CONTAINS]->(n) | file.name][0] as source_file
            LIMIT $limit
            """

            result = session.run(query, **params)
            return [
                {
                    "entity": record["n"],
                    "source_file": record["source_file"]
                }
                for record in result
            ]

    def find_file_knowledge(self, file_node_id: str) -> Dict[str, Any]:
        """
        Find all knowledge extracted from a specific file.

        Args:
            file_node_id: ID of the file node

        Returns:
            Dictionary containing entities and relationships from the file
        """
        driver = self.neo4j_client._get_driver()

        with driver.session(database=self.neo4j_client.connection.database) as session:
            # Find entities contained in the file
            entities_query = """
            MATCH (file {node_id: $file_id})-[:FILE_CONTAINS]->(entity)
            RETURN entity, type(entity) as entity_type
            """

            # Find relationships between entities in the file
            relationships_query = """
            MATCH (file {node_id: $file_id})-[:FILE_CONTAINS]->(e1)
            MATCH (file)-[:FILE_CONTAINS]->(e2)
            MATCH (e1)-[r]->(e2)
            WHERE r.source_file_id = $file_id
            RETURN e1, r, e2, type(r) as relationship_type
            """

            entities_result = session.run(entities_query, file_id=file_node_id)
            relationships_result = session.run(relationships_query, file_id=file_node_id)

            return {
                "file_id": file_node_id,
                "entities": [
                    {
                        "entity": record["entity"],
                        "type": record["entity_type"]
                    }
                    for record in entities_result
                ],
                "relationships": [
                    {
                        "source": record["e1"],
                        "relationship": record["r"],
                        "target": record["e2"],
                        "type": record["relationship_type"]
                    }
                    for record in relationships_result
                ]
            }


def create_org_kg_processor(
    neo4j_uri: str = "bolt://localhost:7687",
    neo4j_user: str = "neo4j",
    neo4j_password: str = "cocoindex",
    enable_org_integration: bool = False,
    **extractor_kwargs
) -> OrganizationalKGProcessor:
    """
    Factory function to create an organizational KG processor.

    Args:
        neo4j_uri: Neo4j connection URI
        neo4j_user: Neo4j username
        neo4j_password: Neo4j password
        enable_org_integration: Whether to enable organizational features
        **extractor_kwargs: Additional arguments for the extractor

    Returns:
        Configured OrganizationalKGProcessor
    """
    from storage.neo4j_client import create_default_neo4j_client
    from core.extractor import EntityRelationshipExtractor

    # Create Neo4j client
    neo4j_client = create_default_neo4j_client(
        uri=neo4j_uri,
        user=neo4j_user,
        password=neo4j_password
    )

    # Create extractor
    extractor = EntityRelationshipExtractor(**extractor_kwargs)

    return OrganizationalKGProcessor(
        extractor=extractor,
        neo4j_client=neo4j_client,
        enable_org_integration=enable_org_integration
    )
