#!/usr/bin/env python3
"""
Example: Using document_type parameter for specialized processing

This script demonstrates how to use the document_type parameter
to get specialized prompts for different types of enterprise documents.
"""

import os
from dotenv import load_dotenv
from standalone_processor import create_standalone_processor

# Load environment variables
load_dotenv()

def example_document_type_usage():
    """Demonstrate document type usage."""
    
    # Create processor
    processor = create_standalone_processor(
        neo4j_uri=os.getenv("NEO4J_URI"),
        neo4j_user=os.getenv("NEO4J_USER"),
        neo4j_password=os.getenv("NEO4J_PASSWORD"),
        llm_provider="openrouter",
        llm_model="anthropic/claude-3.5-sonnet"
    )
    
    # Example 1: Process a policy document
    print("🔍 Processing policy document with specialized prompts...")
    policy_results = processor.process_document(
        file_path="../documents/CultureValuesRapid.pdf",
        document_type="policy"
    )
    print(f"Policy processing completed: {policy_results.is_completed}")
    
    # Example 2: Process as a general report
    print("\n📊 Processing same document as a report...")
    report_results = processor.process_document(
        file_path="../documents/CultureValuesRapid.pdf", 
        document_type="report"
    )
    print(f"Report processing completed: {report_results.is_completed}")
    
    # Example 3: Process without document type (default behavior)
    print("\n📄 Processing without document type...")
    default_results = processor.process_document(
        file_path="../documents/CultureValuesRapid.pdf"
    )
    print(f"Default processing completed: {default_results.is_completed}")
    
    processor.neo4j_client.close()

def show_specialized_prompts():
    """Show what specialized prompts look like."""
    from prompt_generator import PromptGenerator
    
    generator = PromptGenerator()
    sample_content = "This is a sample business proposal for implementing a new CRM system."
    
    print("🎯 Specialized Prompts for Different Document Types:")
    print("=" * 60)
    
    document_types = ["report", "proposal", "email", "contract", "policy", "procedure", "meeting_notes"]
    
    for doc_type in document_types:
        print(f"\n📋 {doc_type.upper()} PROMPT:")
        prompt = generator.generate_summarization_prompt(sample_content, doc_type)
        # Show just the specialized instruction part
        lines = prompt.split('\n')
        for line in lines:
            if f"Specialized Instructions for {doc_type}" in line:
                idx = lines.index(line)
                print(lines[idx])
                if idx + 1 < len(lines):
                    print(lines[idx + 1])
                break

if __name__ == "__main__":
    print("📚 Document Type Parameter Examples")
    print("=" * 50)
    
    # Show what the specialized prompts look like
    show_specialized_prompts()
    
    print("\n" + "=" * 50)
    print("🚀 Running actual processing examples...")
    
    # Run actual processing examples
    # example_document_type_usage()  # Uncomment to run actual processing
