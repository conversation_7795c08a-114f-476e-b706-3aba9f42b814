#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced Enterprise KG Features

This script tests all enhanced features including:
- Document chunking with different strategies
- Hybrid search capabilities
- Entity discovery and deduplication
- Graph connectivity and properties
- Requesty LLM provider integration
- Node deduplication verification

Tests the 3 documents in the documents folder:
1. Culture Values - Rapid.pdf
2. JiraSource.json
3. Title_ Project Atlas - AI-driven Customer Insights Platform.docx
"""

import os
import sys
import json
import logging
import asyncio
from typing import List, Dict, Any, Set
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import enhanced components
from enhanced_standalone_processor import EnhancedStandaloneProcessor
from chunking_engine import ChunkingEngine, ChunkingStrategy, create_chunking_engine
from hybrid_search_engine import HybridSearchEngine, SearchMethod, create_hybrid_search_engine
from entity_discovery import EntityDiscoveryEngine, create_entity_discovery_engine
from knowledge_enrichment import KnowledgeEnrichmentEngine, create_knowledge_enrichment_engine

# Import existing components
from standalone_processor import LLMClient, create_standalone_processor
from storage.neo4j_client import Neo4jClient, Neo4jConnection
from constants.schemas import DocumentSummary, EntityRelationship, ProcessingMetadata
from prompt_generator import PromptGenerator, create_full_prompt_generator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedFeaturesTester:
    """Comprehensive tester for enhanced Enterprise KG features."""
    
    def __init__(self):
        """Initialize the tester with all required components."""
        self.documents_dir = "documents"
        self.test_documents = [
            "Culture Values - Rapid.pdf",
            "JiraSource.json", 
            "Title_ Project Atlas - AI-driven Customer Insights Platform.docx"
        ]
        
        # Initialize components
        self.llm_client = None
        self.neo4j_client = None
        self.enhanced_processor = None
        self.chunking_engine = None
        self.hybrid_search_engine = None
        self.entity_discovery_engine = None
        self.knowledge_enrichment_engine = None
        
        # Test results storage
        self.test_results = {}
        self.processing_metadata = {}
        self.extracted_entities = set()
        self.extracted_relationships = []
        
    def setup_components(self) -> bool:
        """Set up all required components for testing."""
        print("🔧 Setting up Enhanced Components")
        print("=" * 50)
        
        try:
            # Setup LLM client with requesty provider
            provider = "requesty"  # Force requesty for testing
            model = os.getenv("LLM_MODEL", "anthropic/claude-3.5-sonnet")
            api_key = os.getenv("REQUESTY_API_KEY") or os.getenv("OPENROUTER_API_KEY")
            
            if not api_key:
                print("✗ No API key found for LLM provider")
                return False
                
            self.llm_client = LLMClient(provider=provider, model=model, api_key=api_key)
            print(f"✓ LLM Client initialized: {provider}/{model}")
            
            # Setup Neo4j client
            neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
            neo4j_user = os.getenv("NEO4J_USER", "neo4j")
            neo4j_password = os.getenv("NEO4J_PASSWORD")
            
            if not neo4j_password:
                print("✗ Neo4j password not set")
                return False
                
            neo4j_conn = Neo4jConnection(
                uri=neo4j_uri,
                user=neo4j_user,
                password=neo4j_password
            )
            self.neo4j_client = Neo4jClient(neo4j_conn)
            print(f"✓ Neo4j Client initialized: {neo4j_uri}")
            
            # Test Neo4j connection
            if not self._test_neo4j_connection():
                return False
                
            # Initialize chunking engine
            self.chunking_engine = create_chunking_engine(
                strategy="hybrid",
                chunk_size=1000,
                chunk_overlap=200
            )
            print("✓ Chunking Engine initialized")
            
            # Initialize entity discovery engine
            self.entity_discovery_engine = create_entity_discovery_engine()
            print("✓ Entity Discovery Engine initialized")
            
            # Initialize knowledge enrichment engine
            self.knowledge_enrichment_engine = create_knowledge_enrichment_engine()
            print("✓ Knowledge Enrichment Engine initialized")
            
            # Initialize enhanced processor
            self.enhanced_processor = EnhancedStandaloneProcessor(
                llm_client=self.llm_client,
                neo4j_client=self.neo4j_client,
                chunking_strategy="hybrid",
                chunk_size=1000,
                chunk_overlap=200,
                enable_hybrid_search=True,
                enable_entity_discovery=True,
                enable_knowledge_enrichment=True
            )
            print("✓ Enhanced Standalone Processor initialized")
            
            print()
            return True
            
        except Exception as e:
            print(f"✗ Component setup failed: {e}")
            return False
    
    def _test_neo4j_connection(self) -> bool:
        """Test Neo4j connection."""
        try:
            driver = self.neo4j_client._get_driver()
            with driver.session() as session:
                result = session.run("RETURN 1 as test")
                test_value = result.single()["test"]
                if test_value == 1:
                    print("✓ Neo4j connection successful")
                    return True
        except Exception as e:
            print(f"✗ Neo4j connection failed: {e}")
            return False
        return False
    
    def test_document_existence(self) -> bool:
        """Test that all required documents exist."""
        print("📁 Testing Document Existence")
        print("=" * 50)
        
        all_exist = True
        for doc in self.test_documents:
            doc_path = os.path.join(self.documents_dir, doc)
            if os.path.exists(doc_path):
                file_size = os.path.getsize(doc_path)
                print(f"✓ {doc} ({file_size:,} bytes)")
            else:
                print(f"✗ {doc} - NOT FOUND")
                all_exist = False
        
        print()
        return all_exist
    
    def test_chunking_strategies(self) -> bool:
        """Test different chunking strategies on documents."""
        print("🔪 Testing Chunking Strategies")
        print("=" * 50)
        
        strategies = [
            ChunkingStrategy.FIXED_SIZE,
            ChunkingStrategy.SENTENCE_BASED,
            ChunkingStrategy.PARAGRAPH_BASED,
            ChunkingStrategy.HYBRID
        ]
        
        test_doc = os.path.join(self.documents_dir, self.test_documents[0])  # Test with PDF
        
        try:
            # Read document content
            content = self._read_document_content(test_doc)
            print(f"Document content length: {len(content)} characters")
            
            for strategy in strategies:
                chunking_engine = create_chunking_engine(
                    strategy=strategy.value,
                    chunk_size=800,
                    chunk_overlap=150
                )
                
                chunks = chunking_engine.chunk_document(content, test_doc)
                
                print(f"✓ {strategy.value}: {len(chunks)} chunks created")
                
                # Verify chunk properties
                total_chars = sum(len(chunk.text) for chunk in chunks)
                avg_chunk_size = total_chars / len(chunks) if chunks else 0
                print(f"  - Average chunk size: {avg_chunk_size:.0f} characters")
                
                # Store results
                self.test_results[f"chunking_{strategy.value}"] = {
                    "chunk_count": len(chunks),
                    "total_characters": total_chars,
                    "average_size": avg_chunk_size
                }
            
            print()
            return True
            
        except Exception as e:
            print(f"✗ Chunking test failed: {e}")
            return False
    
    def _read_document_content(self, file_path: str) -> str:
        """Read document content based on file type."""
        file_extension = os.path.splitext(file_path)[1].lower()
        
        if file_extension == '.pdf':
            return self._read_pdf(file_path)
        elif file_extension == '.docx':
            return self._read_docx(file_path)
        elif file_extension == '.json':
            return self._read_json(file_path)
        else:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
    
    def _read_pdf(self, file_path: str) -> str:
        """Extract text from PDF file."""
        try:
            import pypdf
            with open(file_path, 'rb') as f:
                pdf_reader = pypdf.PdfReader(f)
                text_content = []
                for page in pdf_reader.pages:
                    text_content.append(page.extract_text())
                return '\n'.join(text_content)
        except ImportError:
            print("⚠️  pypdf not installed. Install with: pip install pypdf")
            return ""
    
    def _read_docx(self, file_path: str) -> str:
        """Extract text from DOCX file."""
        try:
            from docx import Document
            doc = Document(file_path)
            text_content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            return '\n'.join(text_content)
        except ImportError:
            print("⚠️  python-docx not installed. Install with: pip install python-docx")
            return ""
    
    def _read_json(self, file_path: str) -> str:
        """Extract and format content from JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            return json.dumps(json_data, indent=2, ensure_ascii=False)
        except json.JSONDecodeError as e:
            print(f"⚠️  Invalid JSON in file {file_path}: {e}")
            return ""

    def test_document_processing(self) -> bool:
        """Test enhanced document processing with all 3 documents."""
        print("📄 Testing Enhanced Document Processing")
        print("=" * 50)

        if not self.enhanced_processor:
            print("✗ Enhanced processor not available (setup failed)")
            return False

        try:
            # Clear existing data first
            self._clear_neo4j_data()

            processing_results = []

            for doc in self.test_documents:
                doc_path = os.path.join(self.documents_dir, doc)
                print(f"Processing: {doc}")

                # Process document with enhanced processor
                metadata = self.enhanced_processor.process_document(
                    file_path=doc_path,
                    enable_chunking=True
                )

                processing_results.append(metadata)

                # Store metadata
                self.processing_metadata[doc] = metadata

                print(f"  ✓ Processed in {metadata.processing_duration_seconds:.2f}s")
                print(f"  ✓ Entity extraction: {metadata.entity_extraction_completed}")
                print(f"  ✓ Relationship extraction: {metadata.relationship_extraction_completed}")
                print(f"  ✓ Graph storage: {metadata.graph_storage_completed}")

                if metadata.errors:
                    print(f"  ⚠️  Errors: {metadata.errors}")

            # Verify all documents were processed successfully
            successful_docs = sum(1 for m in processing_results if m.is_completed)
            print(f"\n✓ Successfully processed {successful_docs}/{len(self.test_documents)} documents")

            return successful_docs == len(self.test_documents)

        except Exception as e:
            print(f"✗ Document processing failed: {e}")
            return False

    def test_graph_connectivity(self) -> bool:
        """Test that the graph is properly connected with no duplicate nodes."""
        print("🕸️  Testing Graph Connectivity and Node Deduplication")
        print("=" * 50)

        try:
            # Query for all nodes and relationships
            node_query = """
                MATCH (n:Entity)
                RETURN n.name as name, n.type as type, n.source_document as source,
                       id(n) as node_id, labels(n) as labels
                ORDER BY n.name
            """

            relationship_query = """
                MATCH (a:Entity)-[r]->(b:Entity)
                RETURN a.name as source, type(r) as relationship, b.name as target,
                       r.confidence_score as confidence, r.source_document as source_doc
                ORDER BY a.name, b.name
            """

            # Execute queries
            nodes = list(self.neo4j_client.execute_query(node_query))
            relationships = list(self.neo4j_client.execute_query(relationship_query))

            print(f"✓ Found {len(nodes)} nodes in the graph")
            print(f"✓ Found {len(relationships)} relationships in the graph")

            # Check for duplicate nodes (same name and type)
            node_signatures = {}
            duplicates = []

            for node in nodes:
                signature = (node['name'].lower().strip(), node['type'])
                if signature in node_signatures:
                    duplicates.append({
                        'name': node['name'],
                        'type': node['type'],
                        'existing_id': node_signatures[signature],
                        'duplicate_id': node['node_id']
                    })
                else:
                    node_signatures[signature] = node['node_id']

            if duplicates:
                print(f"✗ Found {len(duplicates)} duplicate nodes:")
                for dup in duplicates[:5]:  # Show first 5
                    print(f"  - {dup['name']} ({dup['type']})")
                return False
            else:
                print("✓ No duplicate nodes found")

            # Check graph connectivity
            connectivity_query = """
                MATCH (n:Entity)
                OPTIONAL MATCH (n)-[r]-(connected)
                WITH n, count(r) as connections
                RETURN
                    count(n) as total_nodes,
                    count(CASE WHEN connections > 0 THEN 1 END) as connected_nodes,
                    count(CASE WHEN connections = 0 THEN 1 END) as isolated_nodes,
                    avg(connections) as avg_connections
            """

            connectivity_result = list(self.neo4j_client.execute_query(connectivity_query))[0]

            total_nodes = connectivity_result['total_nodes']
            connected_nodes = connectivity_result['connected_nodes']
            isolated_nodes = connectivity_result['isolated_nodes']
            avg_connections = connectivity_result['avg_connections'] or 0

            print(f"✓ Graph connectivity:")
            print(f"  - Total nodes: {total_nodes}")
            print(f"  - Connected nodes: {connected_nodes}")
            print(f"  - Isolated nodes: {isolated_nodes}")
            print(f"  - Average connections per node: {avg_connections:.2f}")

            # Store results
            self.test_results['graph_analysis'] = {
                'total_nodes': total_nodes,
                'total_relationships': len(relationships),
                'connected_nodes': connected_nodes,
                'isolated_nodes': isolated_nodes,
                'avg_connections': avg_connections,
                'has_duplicates': len(duplicates) > 0,
                'duplicate_count': len(duplicates)
            }

            # A good graph should have most nodes connected
            connectivity_ratio = connected_nodes / total_nodes if total_nodes > 0 else 0

            if connectivity_ratio >= 0.7:  # At least 70% of nodes should be connected
                print(f"✓ Good connectivity: {connectivity_ratio:.1%} of nodes are connected")
                return True
            else:
                print(f"⚠️  Low connectivity: {connectivity_ratio:.1%} of nodes are connected")
                return False

        except Exception as e:
            print(f"✗ Graph connectivity test failed: {e}")
            return False

    def test_entity_properties(self) -> bool:
        """Test that entities have proper properties and metadata."""
        print("🏷️  Testing Entity Properties and Metadata")
        print("=" * 50)

        try:
            # Query for entity properties
            properties_query = """
                MATCH (n:Entity)
                RETURN n.name as name, n.type as type, n.source_document as source_doc,
                       n.confidence_score as confidence, n.created_at as created_at,
                       keys(n) as properties
                LIMIT 20
            """

            entities = list(self.neo4j_client.execute_query(properties_query))

            if not entities:
                print("✗ No entities found in the graph")
                return False

            print(f"✓ Analyzing properties of {len(entities)} sample entities")

            # Check required properties
            required_props = ['name', 'type', 'source_document']
            entities_with_all_props = 0
            entities_with_confidence = 0
            entities_with_timestamp = 0

            for entity in entities:
                has_all_required = all(entity.get(prop) for prop in required_props)
                if has_all_required:
                    entities_with_all_props += 1

                if entity.get('confidence'):
                    entities_with_confidence += 1

                if entity.get('created_at'):
                    entities_with_timestamp += 1

            print(f"✓ Entities with all required properties: {entities_with_all_props}/{len(entities)}")
            print(f"✓ Entities with confidence scores: {entities_with_confidence}/{len(entities)}")
            print(f"✓ Entities with timestamps: {entities_with_timestamp}/{len(entities)}")

            # Show sample entities
            print("\n📋 Sample entities:")
            for entity in entities[:5]:
                print(f"  - {entity['name']} ({entity['type']}) from {entity['source_doc']}")

            # Store results
            self.test_results['entity_properties'] = {
                'total_sampled': len(entities),
                'with_required_props': entities_with_all_props,
                'with_confidence': entities_with_confidence,
                'with_timestamps': entities_with_timestamp
            }

            # Success if most entities have required properties
            success_ratio = entities_with_all_props / len(entities)
            return success_ratio >= 0.8  # At least 80% should have required properties

        except Exception as e:
            print(f"✗ Entity properties test failed: {e}")
            return False

    def _clear_neo4j_data(self):
        """Clear existing data from Neo4j for clean testing."""
        try:
            clear_query = "MATCH (n) DETACH DELETE n"
            self.neo4j_client.execute_query(clear_query)
            print("✓ Cleared existing Neo4j data")
        except Exception as e:
            print(f"⚠️  Failed to clear Neo4j data: {e}")


    def test_hybrid_search(self) -> bool:
        """Test hybrid search capabilities."""
        print("🔍 Testing Hybrid Search Capabilities")
        print("=" * 50)

        if not self.enhanced_processor:
            print("✗ Enhanced processor not available (setup failed)")
            return False

        if not self.enhanced_processor.enable_hybrid_search:
            print("⚠️  Hybrid search not enabled, skipping test")
            return True

        try:
            # Test queries
            test_queries = [
                "Who is working on Project Atlas?",
                "What are the company values?",
                "Show me information about Jira",
                "What projects are mentioned?",
                "Who are the key people?"
            ]

            search_results = {}

            for query in test_queries:
                print(f"Testing query: '{query}'")

                try:
                    # Test different search methods
                    methods = ["auto_hybrid", "semantic_first", "graph_first"]

                    for method in methods:
                        result = self.enhanced_processor.search(
                            query=query,
                            method=method,
                            top_k=5
                        )

                        if "error" not in result:
                            print(f"  ✓ {method}: Found results")
                        else:
                            print(f"  ⚠️  {method}: {result['error']}")

                    search_results[query] = result

                except Exception as e:
                    print(f"  ✗ Query failed: {e}")
                    search_results[query] = {"error": str(e)}

            # Store results
            self.test_results['hybrid_search'] = {
                'queries_tested': len(test_queries),
                'successful_queries': sum(1 for r in search_results.values() if "error" not in r),
                'results': search_results
            }

            successful_queries = sum(1 for r in search_results.values() if "error" not in r)
            success_ratio = successful_queries / len(test_queries)

            print(f"\n✓ Hybrid search test completed: {successful_queries}/{len(test_queries)} queries successful")

            return success_ratio >= 0.6  # At least 60% of queries should work

        except Exception as e:
            print(f"✗ Hybrid search test failed: {e}")
            return False

    def test_entity_discovery(self) -> bool:
        """Test entity discovery and enrichment capabilities."""
        print("🔬 Testing Entity Discovery and Enrichment")
        print("=" * 50)

        if not self.enhanced_processor:
            print("✗ Enhanced processor not available (setup failed)")
            return False

        if not self.enhanced_processor.enable_entity_discovery:
            print("⚠️  Entity discovery not enabled, skipping test")
            return True

        try:
            # Get some sample search results to test entity discovery
            sample_results = [
                {"metadata": {"chunk_text": "John Smith is the project manager for Project Atlas"}},
                {"metadata": {"chunk_text": "Sarah Johnson works on the analytics dashboard"}},
                {"metadata": {"chunk_text": "The CRM system is integrated with the platform"}},
            ]

            # Test entity discovery
            discovered_entities = self.enhanced_processor.discover_entities(sample_results)

            print(f"✓ Discovered {len(discovered_entities)} entities from sample data")

            # Show sample discovered entities
            for entity in discovered_entities[:5]:
                print(f"  - {entity['name']} ({entity['type']}) - confidence: {entity['confidence']:.2f}")

            # Store results
            self.test_results['entity_discovery'] = {
                'entities_discovered': len(discovered_entities),
                'sample_entities': discovered_entities[:10]  # Store first 10 for analysis
            }

            return len(discovered_entities) > 0

        except Exception as e:
            print(f"✗ Entity discovery test failed: {e}")
            return False

    def test_knowledge_enrichment(self) -> bool:
        """Test knowledge enrichment and query templates."""
        print("🧠 Testing Knowledge Enrichment")
        print("=" * 50)

        if not self.enhanced_processor:
            print("✗ Enhanced processor not available (setup failed)")
            return False

        if not self.enhanced_processor.enable_knowledge_enrichment:
            print("⚠️  Knowledge enrichment not enabled, skipping test")
            return True

        try:
            # Test query enrichment
            test_queries = [
                "Who works on projects?",
                "What are the relationships between people?",
                "Show me organizational structure",
                "Find project dependencies"
            ]

            enrichment_results = {}

            for query in test_queries:
                print(f"Enriching query: '{query}'")

                try:
                    enriched = self.enhanced_processor.enrich_query(query)

                    if enriched:
                        print(f"  ✓ Generated {len(enriched)} enriched query options")
                        for i, option in enumerate(enriched[:2]):  # Show first 2
                            print(f"    {i+1}. Template: {option['template']}")
                    else:
                        print(f"  ⚠️  No enrichment options generated")

                    enrichment_results[query] = enriched

                except Exception as e:
                    print(f"  ✗ Enrichment failed: {e}")
                    enrichment_results[query] = []

            # Store results
            self.test_results['knowledge_enrichment'] = {
                'queries_tested': len(test_queries),
                'successful_enrichments': sum(1 for r in enrichment_results.values() if r),
                'results': enrichment_results
            }

            successful_enrichments = sum(1 for r in enrichment_results.values() if r)
            success_ratio = successful_enrichments / len(test_queries)

            print(f"\n✓ Knowledge enrichment test completed: {successful_enrichments}/{len(test_queries)} queries enriched")

            return success_ratio >= 0.5  # At least 50% should be enriched

        except Exception as e:
            print(f"✗ Knowledge enrichment test failed: {e}")
            return False

    def generate_test_report(self) -> Dict[str, Any]:
        """Generate a comprehensive test report."""
        print("📋 Generating Test Report")
        print("=" * 50)

        report = {
            "test_timestamp": datetime.now().isoformat(),
            "documents_tested": self.test_documents,
            "test_results": self.test_results,
            "processing_metadata": {
                doc: {
                    "processing_time": meta.processing_duration_seconds,
                    "entity_extraction": meta.entity_extraction_completed,
                    "relationship_extraction": meta.relationship_extraction_completed,
                    "graph_storage": meta.graph_storage_completed,
                    "errors": meta.errors
                }
                for doc, meta in self.processing_metadata.items()
            }
        }

        # Save report to file
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            print(f"✓ Test report saved to: {report_file}")
        except Exception as e:
            print(f"⚠️  Failed to save test report: {e}")

        return report


def main():
    """Main test execution function."""
    print("🚀 Enhanced Enterprise KG Features Test Suite")
    print("=" * 60)
    print()

    tester = EnhancedFeaturesTester()

    # Run tests in order
    tests = [
        ("Document Existence", tester.test_document_existence),
        ("Component Setup", tester.setup_components),
        ("Chunking Strategies", tester.test_chunking_strategies),
        ("Document Processing", tester.test_document_processing),
        ("Graph Connectivity", tester.test_graph_connectivity),
        ("Entity Properties", tester.test_entity_properties),
        ("Hybrid Search", tester.test_hybrid_search),
        ("Entity Discovery", tester.test_entity_discovery),
        ("Knowledge Enrichment", tester.test_knowledge_enrichment),
    ]

    results = {}

    for test_name, test_func in tests:
        try:
            print(f"Running {test_name}...")
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            logger.exception(f"Detailed error for {test_name}:")
            results[test_name] = False
        print()

    # Generate comprehensive report
    try:
        report = tester.generate_test_report()
        print()
    except Exception as e:
        print(f"⚠️  Failed to generate test report: {e}")

    # Print summary
    print("📊 Final Test Summary")
    print("=" * 60)

    passed = sum(1 for result in results.values() if result)
    total = len(results)

    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status} {test_name}")

    print()
    print(f"Results: {passed}/{total} tests passed ({passed/total:.1%})")

    if passed == total:
        print("🎉 All tests passed! Enhanced features are working correctly.")
        print("\n✅ Key Achievements:")
        print("  - Document chunking with multiple strategies")
        print("  - Connected graph with no duplicate nodes")
        print("  - Proper entity properties and metadata")
        print("  - Hybrid search capabilities")
        print("  - Entity discovery and enrichment")
        print("  - Requesty LLM provider integration")
    elif passed >= total * 0.8:
        print("✅ Most tests passed! System is largely functional.")
        print("⚠️  Check failed tests for minor issues.")
    else:
        print("⚠️  Several tests failed. Check the errors above.")
        print("💡 Common issues:")
        print("  - Check Neo4j connection and credentials")
        print("  - Verify LLM API key is set correctly")
        print("  - Ensure all required packages are installed")

    return 0 if passed >= total * 0.8 else 1


if __name__ == "__main__":
    sys.exit(main())
