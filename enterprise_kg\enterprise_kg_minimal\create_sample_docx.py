#!/usr/bin/env python3
"""
Create a sample .docx document for testing the enterprise KG system.
"""

import os
from docx import Document


def create_sample_docx():
    """Create a sample .docx document with enterprise content."""
    
    # Create documents directory if it doesn't exist
    docs_dir = "documents"
    if not os.path.exists(docs_dir):
        os.makedirs(docs_dir)
    
    # Create the document
    doc = Document()
    
    # Add title
    title = doc.add_heading('Q4 2024 Enterprise Project Status Report', 0)
    
    # Add executive summary
    doc.add_heading('Executive Summary', level=1)
    doc.add_paragraph(
        'This quarterly report provides an overview of key enterprise projects and initiatives. '
        'The report covers project status, team assignments, and strategic objectives for Q4 2024.'
    )
    
    # Add project details
    doc.add_heading('Project Alpha - Digital Transformation', level=1)
    doc.add_paragraph(
        'Project Alpha is led by <PERSON>, who works for the Technology Division. '
        'She manages a cross-functional team focused on digital transformation initiatives. '
        'The project involves collaboration with external partners including TechCorp Solutions.'
    )
    
    doc.add_paragraph(
        'Key team members include <PERSON>, who is involved in the system architecture, '
        'and <PERSON>, who reports to <PERSON> and handles user experience design. '
        'The project mentions integration with existing CRM systems and mentions the need for '
        'enhanced security protocols.'
    )
    
    # Add project table
    doc.add_heading('Team Assignments', level=2)
    table = doc.add_table(rows=1, cols=4)
    table.style = 'Table Grid'
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Name'
    hdr_cells[1].text = 'Role'
    hdr_cells[2].text = 'Department'
    hdr_cells[3].text = 'Project'
    
    # Add team data
    team_data = [
        ('Jennifer Martinez', 'Project Manager', 'Technology Division', 'Project Alpha'),
        ('Robert Chen', 'System Architect', 'Engineering', 'Project Alpha'),
        ('Maria Rodriguez', 'UX Designer', 'Design', 'Project Alpha'),
        ('David Kim', 'Security Analyst', 'Cybersecurity', 'Project Alpha'),
        ('Sarah Thompson', 'Business Analyst', 'Strategy', 'Project Beta')
    ]
    
    for name, role, dept, project in team_data:
        row_cells = table.add_row().cells
        row_cells[0].text = name
        row_cells[1].text = role
        row_cells[2].text = dept
        row_cells[3].text = project
    
    # Add another project section
    doc.add_heading('Project Beta - Customer Analytics Platform', level=1)
    doc.add_paragraph(
        'Project Beta is managed by Sarah Thompson, who works for the Strategy Department. '
        'This initiative focuses on developing advanced customer analytics capabilities. '
        'The project involves partnerships with DataCorp Analytics and mentions integration '
        'with existing marketing automation tools.'
    )
    
    doc.add_paragraph(
        'Michael Johnson is involved in the data engineering aspects and reports to Sarah Thompson. '
        'The project mentions the need for real-time data processing and mentions compliance '
        'with data privacy regulations. Lisa Wang manages the quality assurance process.'
    )
    
    # Add organizational relationships
    doc.add_heading('Organizational Structure', level=1)
    doc.add_paragraph(
        'The Technology Division is led by Jennifer Martinez, who reports to the CTO. '
        'The Strategy Department is managed by Sarah Thompson, who works for the Chief Strategy Officer. '
        'Cross-departmental collaboration involves regular meetings between project managers.'
    )
    
    # Add partnerships section
    doc.add_heading('External Partnerships', level=1)
    doc.add_paragraph(
        'Our organization maintains strategic partnerships with several key vendors. '
        'TechCorp Solutions provides technology consulting services and is involved in Project Alpha. '
        'DataCorp Analytics supports our data initiatives and mentions expertise in machine learning. '
        'Both partners work for long-term strategic objectives.'
    )
    
    # Save the document
    file_path = os.path.join(docs_dir, "enterprise_project_report.docx")
    doc.save(file_path)
    
    print(f"✓ Created sample .docx document: {file_path}")
    print("📄 Document contains:")
    print("   - Executive summary")
    print("   - Project details with team assignments")
    print("   - Organizational relationships")
    print("   - External partnerships")
    print("   - Tables with structured data")
    print("\n🎯 This document is ready for knowledge graph extraction!")
    
    return file_path


if __name__ == "__main__":
    create_sample_docx()
