#!/usr/bin/env python3
"""
Test Node Merging in Enterprise KG

This script tests whether nodes are properly merged when the same entity
appears in multiple relationships across different documents.
"""

import os
from dotenv import load_dotenv
from constants.schemas import EntityRelationship
from storage.neo4j_client import Neo4j<PERSON>lient, Neo4jConnection

# Load environment variables
load_dotenv()

def test_node_merging():
    """Test that nodes are properly merged across multiple relationships."""
    
    # Create Neo4j client
    conn = Neo4jConnection(
        uri=os.getenv('NEO4J_URI'),
        user=os.getenv('NEO4J_USER'), 
        password=os.getenv('NEO4J_PASSWORD'),
        database=os.getenv('NEO4J_DATABASE')
    )
    client = Neo4jClient(conn)
    
    print("🧪 Testing Node Merging in Enterprise KG")
    print("=" * 50)
    
    # Clear existing data for clean test
    driver = client._get_driver()
    with driver.session(database=client.connection.database) as session:
        session.run('MATCH (n) DETACH DELETE n')
        print("✅ Cleared existing data")
    
    # Test Case 1: Same person in multiple relationships from Document 1
    print("\n📄 Document 1: Creating relationships with '<PERSON><PERSON>'")
    
    relationships_doc1 = [
        EntityRelationship(
            subject="Priya",
            predicate="works_for",
            object="Engineering Department",
            subject_type="Person",
            object_type="Department",
            confidence_score=0.9,
            context="Employee assignment",
            source_sentence="Priya works for the Engineering Department"
        ),
        EntityRelationship(
            subject="Priya",
            predicate="manages",
            object="AI Project",
            subject_type="Person",
            object_type="Project",
            confidence_score=0.8,
            context="Project management",
            source_sentence="Priya manages the AI Project"
        )
    ]
    
    # Store relationships from Document 1
    results1 = client.batch_create_entity_relationships(relationships_doc1, "document1.pdf")
    print(f"✅ Stored {len(results1)} relationships from Document 1")
    
    # Check current state
    with driver.session(database=client.connection.database) as session:
        result = session.run("MATCH (p:Person {name: 'Priya'}) RETURN count(p) as priya_count")
        priya_count = result.single()['priya_count']
        print(f"📊 Priya nodes after Document 1: {priya_count}")
        
        result = session.run("MATCH (n) RETURN count(n) as total_nodes")
        total_nodes = result.single()['total_nodes']
        print(f"📊 Total nodes after Document 1: {total_nodes}")
    
    # Test Case 2: Same person in different relationships from Document 2
    print("\n📄 Document 2: Adding more relationships with 'Priya'")
    
    relationships_doc2 = [
        EntityRelationship(
            subject="Priya",
            predicate="reports_to",
            object="John Smith",
            subject_type="Person",
            object_type="Person",
            confidence_score=0.9,
            context="Reporting structure",
            source_sentence="Priya reports to John Smith"
        ),
        EntityRelationship(
            subject="Priya",
            predicate="uses",
            object="Slack",
            subject_type="Person",
            object_type="Tool",
            confidence_score=0.7,
            context="Communication tools",
            source_sentence="Priya uses Slack for communication"
        )
    ]
    
    # Store relationships from Document 2
    results2 = client.batch_create_entity_relationships(relationships_doc2, "document2.pdf")
    print(f"✅ Stored {len(results2)} relationships from Document 2")
    
    # Check final state
    with driver.session(database=client.connection.database) as session:
        result = session.run("MATCH (p:Person {name: 'Priya'}) RETURN count(p) as priya_count")
        priya_count_final = result.single()['priya_count']
        print(f"📊 Priya nodes after Document 2: {priya_count_final}")
        
        result = session.run("MATCH (n) RETURN count(n) as total_nodes")
        total_nodes_final = result.single()['total_nodes']
        print(f"📊 Total nodes after Document 2: {total_nodes_final}")
        
        # Get all relationships involving Priya
        result = session.run("""
            MATCH (priya:Person {name: 'Priya'})-[r]-(other)
            RETURN type(r) as relationship_type, 
                   other.name as other_entity,
                   labels(other)[0] as other_type,
                   r.source_document as source_doc
            ORDER BY relationship_type
        """)
        
        print(f"\n🔗 All relationships involving 'Priya':")
        for record in result:
            rel_type = record['relationship_type']
            other_name = record['other_entity']
            other_type = record['other_type']
            source_doc = record['source_doc']
            print(f"   Priya --{rel_type}--> {other_name} ({other_type}) [from {source_doc}]")
    
    # Test Case 3: Verify node properties are updated correctly
    print(f"\n🔍 Checking Priya's node properties:")
    with driver.session(database=client.connection.database) as session:
        result = session.run("""
            MATCH (priya:Person {name: 'Priya'})
            RETURN priya.name as name,
                   priya.entity_type as entity_type,
                   priya.created_at as created_at,
                   priya.updated_at as updated_at
        """)
        
        priya_node = result.single()
        if priya_node:
            print(f"   Name: {priya_node['name']}")
            print(f"   Entity Type: {priya_node['entity_type']}")
            print(f"   Created: {priya_node['created_at']}")
            print(f"   Updated: {priya_node['updated_at']}")
    
    # Verification
    print(f"\n✅ VERIFICATION RESULTS:")
    print(f"   - Priya appears in {len(relationships_doc1) + len(relationships_doc2)} relationships")
    print(f"   - Only {priya_count_final} Priya node exists (should be 1)")
    print(f"   - Node merging is {'✅ WORKING' if priya_count_final == 1 else '❌ BROKEN'}")
    
    client.close()
    return priya_count_final == 1

if __name__ == "__main__":
    success = test_node_merging()
    print(f"\n🎯 Node Merging Test: {'PASSED' if success else 'FAILED'}")
