#!/usr/bin/env python3
"""
Offline Test Suite for Enhanced Enterprise KG Features

This script tests enhanced features that don't require Neo4j connectivity:
- Document chunking with different strategies
- Entity extraction using requesty LLM provider
- Document processing pipeline
- Enhanced properties and metadata

Tests the 3 documents in the documents folder without requiring Neo4j.
"""

import os
import sys
import json
import logging
from typing import List, Dict, Any
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import enhanced components
from chunking_engine import ChunkingEngine, ChunkingStrategy, create_chunking_engine
from standalone_processor import LLMClient
from constants.schemas import EntityRelationship
from prompt_generator import PromptGenerator, create_full_prompt_generator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OfflineEnhancedTester:
    """Offline tester for enhanced Enterprise KG features."""
    
    def __init__(self):
        """Initialize the offline tester."""
        self.documents_dir = "documents"
        self.test_documents = [
            "Culture Values - Rapid.pdf",
            "JiraSource.json", 
            "Title_ Project Atlas - AI-driven Customer Insights Platform.docx"
        ]
        
        # Initialize components
        self.llm_client = None
        self.chunking_engine = None
        self.prompt_generator = None
        
        # Test results storage
        self.test_results = {}
        self.extracted_entities = []
        self.extracted_relationships = []
        
    def setup_components(self) -> bool:
        """Set up components for offline testing."""
        print("🔧 Setting up Enhanced Components (Offline Mode)")
        print("=" * 50)
        
        try:
            # Setup LLM client - try requesty first, fallback to openrouter
            provider = "requesty"
            model = os.getenv("LLM_MODEL", "anthropic/claude-3.5-sonnet")
            api_key = os.getenv("REQUESTY_API_KEY")

            # If requesty key not available, fallback to openrouter
            if not api_key:
                provider = "openrouter"
                api_key = os.getenv("OPENROUTER_API_KEY")
                print("⚠️  Requesty API key not found, using OpenRouter as fallback")
            
            if not api_key:
                print("✗ No API key found for LLM provider")
                return False
                
            self.llm_client = LLMClient(provider=provider, model=model, api_key=api_key)
            print(f"✓ LLM Client initialized: {provider}/{model}")
            
            # Initialize chunking engine
            self.chunking_engine = create_chunking_engine(
                strategy="hybrid",
                chunk_size=1000,
                chunk_overlap=200
            )
            print("✓ Chunking Engine initialized")
            
            # Initialize prompt generator
            self.prompt_generator = create_full_prompt_generator()
            print("✓ Prompt Generator initialized")
            
            print()
            return True
            
        except Exception as e:
            print(f"✗ Component setup failed: {e}")
            return False
    
    def test_document_existence(self) -> bool:
        """Test that all required documents exist."""
        print("📁 Testing Document Existence")
        print("=" * 50)
        
        all_exist = True
        for doc in self.test_documents:
            doc_path = os.path.join(self.documents_dir, doc)
            if os.path.exists(doc_path):
                file_size = os.path.getsize(doc_path)
                print(f"✓ {doc} ({file_size:,} bytes)")
            else:
                print(f"✗ {doc} - NOT FOUND")
                all_exist = False
        
        print()
        return all_exist
    
    def test_chunking_strategies(self) -> bool:
        """Test different chunking strategies on all documents."""
        print("🔪 Testing Chunking Strategies on All Documents")
        print("=" * 50)
        
        strategies = [
            ChunkingStrategy.FIXED_SIZE,
            ChunkingStrategy.SENTENCE_BASED,
            ChunkingStrategy.PARAGRAPH_BASED,
            ChunkingStrategy.HYBRID
        ]
        
        try:
            for doc in self.test_documents:
                doc_path = os.path.join(self.documents_dir, doc)
                print(f"\nTesting chunking on: {doc}")
                
                # Read document content
                content = self._read_document_content(doc_path)
                print(f"  Document content length: {len(content)} characters")
                
                for strategy in strategies:
                    chunking_engine = create_chunking_engine(
                        strategy=strategy.value,
                        chunk_size=800,
                        chunk_overlap=150
                    )
                    
                    chunks = chunking_engine.chunk_document(content, doc_path)
                    
                    print(f"  ✓ {strategy.value}: {len(chunks)} chunks")
                    
                    # Store results
                    key = f"chunking_{doc}_{strategy.value}"
                    self.test_results[key] = {
                        "chunk_count": len(chunks),
                        "total_characters": sum(len(chunk.text) for chunk in chunks),
                        "average_size": sum(len(chunk.text) for chunk in chunks) / len(chunks) if chunks else 0
                    }
            
            print()
            return True
            
        except Exception as e:
            print(f"✗ Chunking test failed: {e}")
            return False
    
    def test_entity_extraction(self) -> bool:
        """Test entity extraction using requesty LLM provider."""
        print("🤖 Testing Entity Extraction with Requesty LLM")
        print("=" * 50)
        
        if not self.llm_client:
            print("✗ LLM client not available")
            return False
        
        try:
            total_relationships = 0
            
            for doc in self.test_documents:
                doc_path = os.path.join(self.documents_dir, doc)
                print(f"\nExtracting entities from: {doc}")
                
                # Read and chunk document
                content = self._read_document_content(doc_path)
                chunks = self.chunking_engine.chunk_document(content, doc_path)
                
                doc_relationships = []
                
                # Process first few chunks to test extraction
                for i, chunk in enumerate(chunks[:3]):  # Test first 3 chunks
                    print(f"  Processing chunk {i+1}/{min(3, len(chunks))}...")
                    
                    # Generate extraction prompt
                    prompt = self.prompt_generator.generate_relationship_extraction_prompt(chunk.text)
                    schema_description = self.prompt_generator.get_schema_description()
                    
                    # Extract relationships
                    try:
                        response = self.llm_client.generate_structured_response(prompt, schema_description)

                        if isinstance(response, list):
                            for rel_data in response:
                                try:
                                    rel = EntityRelationship(**rel_data)
                                    doc_relationships.append(rel)
                                except Exception as e:
                                    logger.warning(f"Failed to parse relationship: {rel_data}, error: {e}")

                    except Exception as e:
                        print(f"    ⚠️  Chunk {i+1} extraction failed: {e}")
                        # If this is the first chunk and we're using requesty, try to suggest model fix
                        if i == 0 and self.llm_client.provider == "requesty" and "not supported" in str(e):
                            print(f"    💡 Requesty model issue. Current model: {self.llm_client.model}")
                            print(f"    💡 Try different model formats like: claude-3-5-sonnet, gpt-4o, etc.")
                
                print(f"  ✓ Extracted {len(doc_relationships)} relationships")
                total_relationships += len(doc_relationships)
                self.extracted_relationships.extend(doc_relationships)
                
                # Store sample relationships
                self.test_results[f"extraction_{doc}"] = {
                    "relationship_count": len(doc_relationships),
                    "sample_relationships": [
                        {
                            "subject": rel.subject,
                            "predicate": rel.predicate,
                            "object": rel.object,
                            "confidence": rel.confidence_score
                        }
                        for rel in doc_relationships[:3]  # Store first 3 as samples
                    ]
                }
            
            print(f"\n✓ Total relationships extracted: {total_relationships}")
            return total_relationships > 0
            
        except Exception as e:
            print(f"✗ Entity extraction test failed: {e}")
            return False
    
    def test_entity_deduplication(self) -> bool:
        """Test entity deduplication logic."""
        print("🔍 Testing Entity Deduplication")
        print("=" * 50)
        
        if not self.extracted_relationships:
            print("⚠️  No relationships available for deduplication test")
            return True
        
        try:
            # Collect all entities
            entities = set()
            entity_types = {}
            
            for rel in self.extracted_relationships:
                entities.add(rel.subject.lower().strip())
                entities.add(rel.object.lower().strip())
                
                if rel.subject_type:
                    entity_types[rel.subject.lower().strip()] = rel.subject_type
                if rel.object_type:
                    entity_types[rel.object.lower().strip()] = rel.object_type
            
            print(f"✓ Found {len(entities)} unique entities")
            print(f"✓ Entity types assigned: {len(entity_types)}")
            
            # Check for potential duplicates (similar names)
            potential_duplicates = []
            entity_list = list(entities)
            
            for i, entity1 in enumerate(entity_list):
                for entity2 in entity_list[i+1:]:
                    # Simple similarity check
                    if self._are_similar_entities(entity1, entity2):
                        potential_duplicates.append((entity1, entity2))
            
            if potential_duplicates:
                print(f"⚠️  Found {len(potential_duplicates)} potential duplicate pairs:")
                for dup in potential_duplicates[:5]:  # Show first 5
                    print(f"    - '{dup[0]}' ≈ '{dup[1]}'")
            else:
                print("✓ No obvious duplicate entities found")
            
            # Store results
            self.test_results['entity_analysis'] = {
                'unique_entities': len(entities),
                'typed_entities': len(entity_types),
                'potential_duplicates': len(potential_duplicates),
                'sample_entities': list(entities)[:10]
            }
            
            print()
            return True
            
        except Exception as e:
            print(f"✗ Entity deduplication test failed: {e}")
            return False
    
    def _are_similar_entities(self, entity1: str, entity2: str) -> bool:
        """Check if two entities are similar (potential duplicates)."""
        # Simple similarity checks
        if entity1 == entity2:
            return False  # Same entity, not a duplicate
        
        # Check if one is contained in the other
        if entity1 in entity2 or entity2 in entity1:
            return True
        
        # Check for common abbreviations or variations
        words1 = set(entity1.split())
        words2 = set(entity2.split())
        
        # If they share significant words, might be similar
        if len(words1.intersection(words2)) >= min(len(words1), len(words2)) * 0.7:
            return True
        
        return False
    
    def _read_document_content(self, file_path: str) -> str:
        """Read document content based on file type."""
        file_extension = os.path.splitext(file_path)[1].lower()
        
        if file_extension == '.pdf':
            return self._read_pdf(file_path)
        elif file_extension == '.docx':
            return self._read_docx(file_path)
        elif file_extension == '.json':
            return self._read_json(file_path)
        else:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
    
    def _read_pdf(self, file_path: str) -> str:
        """Extract text from PDF file."""
        try:
            import pypdf
            with open(file_path, 'rb') as f:
                pdf_reader = pypdf.PdfReader(f)
                text_content = []
                for page in pdf_reader.pages:
                    text_content.append(page.extract_text())
                return '\n'.join(text_content)
        except ImportError:
            print("⚠️  pypdf not installed. Install with: pip install pypdf")
            return ""
    
    def _read_docx(self, file_path: str) -> str:
        """Extract text from DOCX file."""
        try:
            from docx import Document
            doc = Document(file_path)
            text_content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            return '\n'.join(text_content)
        except ImportError:
            print("⚠️  python-docx not installed. Install with: pip install python-docx")
            return ""
    
    def _read_json(self, file_path: str) -> str:
        """Extract and format content from JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            return json.dumps(json_data, indent=2, ensure_ascii=False)
        except json.JSONDecodeError as e:
            print(f"⚠️  Invalid JSON in file {file_path}: {e}")
            return ""


def main():
    """Main test execution function."""
    print("🚀 Enhanced Enterprise KG Features Test Suite (Offline Mode)")
    print("=" * 70)
    print("Testing enhanced features without requiring Neo4j connectivity")
    print()
    
    tester = OfflineEnhancedTester()
    
    # Run tests in order
    tests = [
        ("Document Existence", tester.test_document_existence),
        ("Component Setup", tester.setup_components),
        ("Chunking Strategies", tester.test_chunking_strategies),
        ("Entity Extraction", tester.test_entity_extraction),
        ("Entity Deduplication", tester.test_entity_deduplication),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"Running {test_name}...")
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            logger.exception(f"Detailed error for {test_name}:")
            results[test_name] = False
        print()
    
    # Print summary
    print("📊 Final Test Summary")
    print("=" * 70)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status} {test_name}")
    
    print()
    print(f"Results: {passed}/{total} tests passed ({passed/total:.1%})")
    
    if passed == total:
        print("🎉 All offline tests passed! Enhanced features are working correctly.")
        print("\n✅ Key Achievements:")
        print("  - Document chunking with multiple strategies working")
        print("  - Requesty LLM provider integration successful")
        print("  - Entity extraction from all document types")
        print("  - Entity deduplication logic functional")
        print("  - Enhanced properties and metadata support")
    elif passed >= total * 0.8:
        print("✅ Most tests passed! Core enhanced features are functional.")
    else:
        print("⚠️  Several tests failed. Check the errors above.")
    
    return 0 if passed >= total * 0.8 else 1


if __name__ == "__main__":
    sys.exit(main())
