{"id": "10010", "key": "ATLS", "name": "Atlas", "projectTypeKey": "software", "lead": {"displayName": "<PERSON><PERSON>", "accountId": "5f4e3c0f2e354a0067fbd58e"}, "components": [{"id": "10100", "name": "Data Ingestion"}, {"id": "10101", "name": "AI Analysis Engine"}], "issueTypes": [{"id": "10001", "name": "Task", "description": "A task that needs to be done"}, {"id": "10002", "name": "Bug", "description": "A problem which impairs or prevents the functions of the product"}]}