#!/usr/bin/env python3
"""
Comprehensive Test for Hybrid Search Engine

This script tests the hybrid search functionality using:
- Mock semantic search data (simulating Pinecone)
- Real Neo4j graph data from the existing knowledge graph

Usage:
    python test_hybrid_search.py
    python test_hybrid_search.py --query "Who works on Project Alpha?"
    python test_hybrid_search.py --method template_hybrid
"""

import os
import sys
import asyncio
import argparse
import logging
import json
from typing import List, Dict, Any
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from hybrid_search_engine import (
    HybridSearchEngine, 
    SearchMethod, 
    SemanticSearchClient,
    create_hybrid_search_engine
)
from storage.neo4j_client import Neo4jClient, Neo4jConnection

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedSemanticSearchClient(SemanticSearchClient):
    """
    Enhanced semantic search client with more realistic mock data
    that aligns with the actual Neo4j graph data.
    """
    
    def __init__(self, index_name: str = None, api_key: str = None):
        super().__init__(index_name, api_key)
        
        # Enhanced mock data that matches potential real enterprise scenarios
        self.mock_chunks = [
            {
                "id": "chunk_001",
                "score": 0.92,
                "metadata": {
                    "file_id": "project_alpha_overview.md",
                    "org_id": "rapid_innovation",
                    "chunk_text": "Project Alpha is a strategic AI-driven customer insights platform led by John Doe as project manager. The development team includes Sarah Smith (Lead Developer), Mike Johnson (UI Designer), and Lisa Chen (Data Analyst). The project integrates with our existing CRM System and Analytics Dashboard."
                }
            },
            {
                "id": "chunk_002", 
                "score": 0.88,
                "metadata": {
                    "file_id": "team_structure.md",
                    "org_id": "rapid_innovation",
                    "chunk_text": "The Engineering Department at Rapid Innovation consists of multiple teams. Sarah Smith leads the backend development team, while Mike Johnson heads the UI/UX team. Both teams report to the CTO and collaborate closely on customer-facing applications."
                }
            },
            {
                "id": "chunk_003",
                "score": 0.85,
                "metadata": {
                    "file_id": "system_architecture.md", 
                    "org_id": "rapid_innovation",
                    "chunk_text": "Our technology stack includes the CRM System as the central customer data hub, which integrates with the Analytics Dashboard for reporting and the Mobile App for customer interactions. The Data Pipeline processes information from multiple sources."
                }
            },
            {
                "id": "chunk_004",
                "score": 0.82,
                "metadata": {
                    "file_id": "company_culture.md",
                    "org_id": "rapid_innovation", 
                    "chunk_text": "Rapid Innovation values collaboration and innovation. Our core values include customer focus, technical excellence, and continuous learning. The company culture emphasizes cross-functional teamwork and data-driven decision making."
                }
            },
            {
                "id": "chunk_005",
                "score": 0.78,
                "metadata": {
                    "file_id": "quarterly_goals.md",
                    "org_id": "rapid_innovation",
                    "chunk_text": "Q4 objectives include completing Project Alpha development, improving system integration between CRM and Analytics platforms, and expanding the mobile application features. The Design Department is working on new user interface concepts."
                }
            },
            {
                "id": "chunk_006",
                "score": 0.75,
                "metadata": {
                    "file_id": "meeting_notes_oct.md",
                    "org_id": "rapid_innovation",
                    "chunk_text": "October team meeting notes: John Doe reported Project Alpha is 75% complete. Sarah Smith mentioned backend API development is on track. Mike Johnson presented new UI mockups. Lisa Chen shared analytics insights from user behavior data."
                }
            }
        ]
    
    async def search(self, query: str, top_k: int = 10, filters: Dict = None) -> List[Dict[str, Any]]:
        """Enhanced search with query-specific filtering."""
        logger.info(f"Semantic search for: '{query}' (top_k={top_k})")
        
        # Simple relevance scoring based on query keywords
        query_lower = query.lower()
        scored_chunks = []
        
        for chunk in self.mock_chunks:
            chunk_text = chunk["metadata"]["chunk_text"].lower()
            
            # Calculate relevance score based on keyword matches
            relevance_boost = 0
            keywords = query_lower.split()
            
            for keyword in keywords:
                if keyword in chunk_text:
                    relevance_boost += 0.1
            
            # Apply org filter if specified
            if filters and "org_id" in filters:
                if chunk["metadata"]["org_id"] != filters["org_id"]:
                    continue
            
            # Adjust score with relevance boost
            adjusted_score = min(1.0, chunk["score"] + relevance_boost)
            
            scored_chunk = chunk.copy()
            scored_chunk["score"] = adjusted_score
            scored_chunks.append(scored_chunk)
        
        # Sort by score and return top_k
        scored_chunks.sort(key=lambda x: x["score"], reverse=True)
        return scored_chunks[:top_k]


def print_search_results(response, query: str):
    """Print formatted search results."""
    print(f"\n{'='*80}")
    print(f"HYBRID SEARCH RESULTS")
    print(f"{'='*80}")
    print(f"Query: {query}")
    print(f"Method: {response.method}")
    print(f"Confidence: {response.confidence:.2f}")
    print(f"Processing Info: {response.processing_info}")
    
    # Semantic Search Results
    print(f"\n📄 SEMANTIC SEARCH RESULTS ({response.semantic_search['total_chunks']} chunks)")
    print("-" * 50)
    for i, chunk in enumerate(response.semantic_search.get('chunks', []), 1):
        print(f"{i}. {chunk[:200]}{'...' if len(chunk) > 200 else ''}")
    
    # Knowledge Graph Results  
    print(f"\n🔗 KNOWLEDGE GRAPH RESULTS ({response.knowledge_graph['total_relationships']} relationships)")
    print("-" * 50)
    
    if response.knowledge_graph.get('discovered_entities'):
        print(f"Discovered Entities: {', '.join(response.knowledge_graph['discovered_entities'])}")
    
    for i, rel in enumerate(response.knowledge_graph.get('relationships', []), 1):
        # Handle different relationship formats
        if 'e.name' in rel and 't.name' in rel:
            source = rel.get('e.name', 'Unknown')
            target = rel.get('t.name', 'Unknown') 
            rel_type = rel.get('type(r)', 'RELATED_TO')
            source_type = rel.get('e.type', '')
            target_type = rel.get('t.type', '')
        else:
            # Handle other formats
            source = rel.get('person', rel.get('source.name', rel.get('manager', 'Unknown')))
            target = rel.get('target', rel.get('project', rel.get('system.name', 'Unknown')))
            rel_type = rel.get('relationship', rel.get('type(r)', 'RELATED_TO'))
            source_type = rel.get('person_type', rel.get('manager_type', ''))
            target_type = rel.get('target_type', rel.get('project_type', ''))
        
        type_info = f" ({source_type} → {target_type})" if source_type and target_type else ""
        print(f"{i}. {source} --[{rel_type}]--> {target}{type_info}")
    
    # Source Files
    if response.source_files:
        print(f"\n📁 SOURCE FILES ({len(response.source_files)} files)")
        print("-" * 50)
        for file in response.source_files:
            print(f"  - {file}")
    
    # Generated Answer
    print(f"\n💡 GENERATED ANSWER")
    print("-" * 50)
    print(response.answer)
    
    print(f"\n{'='*80}")


async def test_search_method(engine: HybridSearchEngine, query: str, method: SearchMethod):
    """Test a specific search method."""
    print(f"\n🔍 Testing {method.value.upper()} search...")
    
    try:
        response = await engine.search(
            query=query,
            method=method,
            top_k_semantic=3,
            top_k_graph=5,
            org_id="rapid_innovation"
        )
        
        print_search_results(response, query)
        return response
        
    except Exception as e:
        logger.error(f"Search failed for method {method.value}: {e}")
        return None


async def run_comprehensive_test(engine: HybridSearchEngine):
    """Run comprehensive tests with various queries and methods."""
    
    test_queries = [
        "Who works on Project Alpha?",
        "What systems does Sarah Smith use?", 
        "Who manages the development team?",
        "How are CRM System and Analytics Dashboard connected?",
        "Tell me about Rapid Innovation company culture",
        "What projects is Mike Johnson involved in?"
    ]
    
    test_methods = [
        SearchMethod.SEMANTIC_ONLY,
        SearchMethod.GRAPH_ONLY, 
        SearchMethod.TEMPLATE_HYBRID,
        SearchMethod.DISCOVERY_HYBRID,
        SearchMethod.AUTO_HYBRID
    ]
    
    print("🚀 Starting Comprehensive Hybrid Search Test")
    print("="*80)
    
    # Test each query with different methods
    for i, query in enumerate(test_queries, 1):
        print(f"\n📋 TEST CASE {i}: {query}")
        print("="*80)
        
        # Test auto hybrid first (recommended approach)
        await test_search_method(engine, query, SearchMethod.AUTO_HYBRID)
        
        # Optionally test other methods for comparison
        # Uncomment to see detailed comparison
        # for method in [SearchMethod.SEMANTIC_ONLY, SearchMethod.GRAPH_ONLY]:
        #     await test_search_method(engine, query, method)
    
    print("\n✅ Comprehensive test completed!")


async def test_neo4j_data_availability(neo4j_client: Neo4jClient):
    """Test what data is available in Neo4j."""
    print("\n🔍 Checking Neo4j Data Availability...")
    print("-" * 50)

    try:
        # Check total entities
        entity_count_query = "MATCH (e:Entity) RETURN count(e) as total"
        result = neo4j_client.execute_query(entity_count_query)
        total_entities = result[0]["total"] if result else 0
        print(f"Total Entities: {total_entities}")

        # Check entity types
        entity_types_query = "MATCH (e:Entity) RETURN DISTINCT e.type as type, count(e) as count ORDER BY count DESC"
        result = neo4j_client.execute_query(entity_types_query)
        print("Entity Types:")
        for record in result[:10]:
            print(f"  - {record['type']}: {record['count']}")

        # Check total relationships
        rel_count_query = "MATCH ()-[r]->() RETURN count(r) as total"
        result = neo4j_client.execute_query(rel_count_query)
        total_rels = result[0]["total"] if result else 0
        print(f"Total Relationships: {total_rels}")

        # Check relationship types
        rel_types_query = "MATCH ()-[r]->() RETURN DISTINCT type(r) as type, count(r) as count ORDER BY count DESC"
        result = neo4j_client.execute_query(rel_types_query)
        print("Relationship Types:")
        for record in result[:10]:
            print(f"  - {record['type']}: {record['count']}")

        # Sample entities
        sample_query = "MATCH (e:Entity) RETURN e.name, e.type LIMIT 10"
        result = neo4j_client.execute_query(sample_query)
        print("Sample Entities:")
        for record in result:
            print(f"  - {record['e.name']} ({record['e.type']})")

        return total_entities > 0 and total_rels > 0

    except Exception as e:
        logger.error(f"Failed to check Neo4j data: {e}")
        return False


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Test Hybrid Search Engine")

    parser.add_argument(
        "--query", "-q",
        help="Single query to test (if not provided, runs comprehensive test)"
    )

    parser.add_argument(
        "--method", "-m",
        choices=["semantic_only", "graph_only", "template_hybrid", "discovery_hybrid", "auto_hybrid"],
        default="auto_hybrid",
        help="Search method to use (default: auto_hybrid)"
    )

    parser.add_argument(
        "--neo4j-uri",
        default=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        help="Neo4j URI"
    )

    parser.add_argument(
        "--neo4j-user",
        default=os.getenv("NEO4J_USER", "neo4j"),
        help="Neo4j username"
    )

    parser.add_argument(
        "--neo4j-password",
        default=os.getenv("NEO4J_PASSWORD", "password"),
        help="Neo4j password"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )

    return parser.parse_args()


async def main():
    """Main test function."""
    args = parse_arguments()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    print("🧪 Hybrid Search Engine Test")
    print("="*50)

    # Step 1: Create Neo4j client
    print("🔗 Connecting to Neo4j...")
    try:
        neo4j_conn = Neo4jConnection(
            uri=args.neo4j_uri,
            user=args.neo4j_user,
            password=args.neo4j_password
        )
        neo4j_client = Neo4jClient(neo4j_conn)

        # Test connection
        driver = neo4j_client._get_driver()
        with driver.session() as session:
            session.run("RETURN 1")
        print("✓ Neo4j connection successful")

    except Exception as e:
        print(f"✗ Neo4j connection failed: {e}")
        return 1

    # Step 2: Check data availability
    has_data = await test_neo4j_data_availability(neo4j_client)
    if not has_data:
        print("\n⚠️  Warning: No data found in Neo4j. Consider running main.py first to populate the graph.")
        print("   Example: python main.py --create-samples")

    # Step 3: Create enhanced semantic client
    print("\n🔍 Initializing enhanced semantic search client...")
    semantic_client = EnhancedSemanticSearchClient()

    # Step 4: Create hybrid search engine
    print("🚀 Creating hybrid search engine...")
    engine = HybridSearchEngine(
        neo4j_client=neo4j_client,
        semantic_client=semantic_client,
        default_method=SearchMethod.AUTO_HYBRID
    )

    # Step 5: Run tests
    try:
        if args.query:
            # Single query test
            method = SearchMethod(args.method)
            await test_search_method(engine, args.query, method)
        else:
            # Comprehensive test
            await run_comprehensive_test(engine)

    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return 1
    finally:
        neo4j_client.close()

    print("\n✅ Test completed successfully!")
    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
