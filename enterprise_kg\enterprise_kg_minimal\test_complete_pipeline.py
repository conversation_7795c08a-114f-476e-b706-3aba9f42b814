#!/usr/bin/env python3
"""
Complete Pipeline Test for Enhanced Enterprise KG Features

This script demonstrates the complete enhanced pipeline working with OpenRouter
as LLM provider, showing all features including entity extraction and graph formation.
"""

import os
import sys
import json
import logging
from typing import List, Dict, Any
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import enhanced components
from chunking_engine import ChunkingEngine, ChunkingStrategy, create_chunking_engine
from standalone_processor import LLMClient
from constants.schemas import EntityRelationship
from prompt_generator import PromptGenerator, create_full_prompt_generator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CompletePipelineTester:
    """Complete pipeline tester for enhanced Enterprise KG features."""
    
    def __init__(self):
        """Initialize the complete pipeline tester."""
        self.documents_dir = "documents"
        self.test_documents = [
            "Culture Values - Rapid.pdf",
            "JiraSource.json", 
            "Title_ Project Atlas - AI-driven Customer Insights Platform.docx"
        ]
        
        # Initialize components
        self.llm_client = None
        self.chunking_engine = None
        self.prompt_generator = None
        
        # Results storage
        self.extracted_relationships = []
        self.unique_entities = set()
        self.entity_types = {}
        
    def setup_components(self) -> bool:
        """Set up components for complete pipeline testing."""
        print("🔧 Setting up Complete Pipeline Components")
        print("=" * 50)
        
        try:
            # Use OpenRouter for reliable testing
            provider = "openrouter"
            model = "anthropic/claude-3.5-sonnet"
            api_key = os.getenv("OPENROUTER_API_KEY")
            
            if not api_key:
                print("✗ OpenRouter API key not found")
                return False
                
            self.llm_client = LLMClient(provider=provider, model=model, api_key=api_key)
            print(f"✓ LLM Client initialized: {provider}/{model}")
            
            # Initialize chunking engine with hybrid strategy
            self.chunking_engine = create_chunking_engine(
                strategy="hybrid",
                chunk_size=800,
                chunk_overlap=150
            )
            print("✓ Chunking Engine initialized (hybrid strategy)")
            
            # Initialize prompt generator
            self.prompt_generator = create_full_prompt_generator()
            print("✓ Prompt Generator initialized")
            
            print()
            return True
            
        except Exception as e:
            print(f"✗ Component setup failed: {e}")
            return False
    
    def test_complete_pipeline(self) -> bool:
        """Test the complete enhanced pipeline on all documents."""
        print("🚀 Testing Complete Enhanced Pipeline")
        print("=" * 50)
        
        if not self.llm_client:
            print("✗ Components not initialized")
            return False
        
        try:
            total_relationships = 0
            
            for doc in self.test_documents:
                doc_path = os.path.join(self.documents_dir, doc)
                print(f"\n📄 Processing: {doc}")
                
                # Step 1: Read document
                content = self._read_document_content(doc_path)
                print(f"  📖 Content length: {len(content)} characters")
                
                # Step 2: Advanced chunking
                chunks = self.chunking_engine.chunk_document(content, doc_path)
                print(f"  🔪 Created {len(chunks)} chunks using hybrid strategy")
                
                # Step 3: Entity extraction from chunks
                doc_relationships = []
                for i, chunk in enumerate(chunks[:2]):  # Process first 2 chunks for demo
                    print(f"    Processing chunk {i+1}/{min(2, len(chunks))}...")
                    
                    # Generate extraction prompt
                    prompt = self.prompt_generator.generate_relationship_extraction_prompt(chunk.text)
                    schema_description = self.prompt_generator.get_schema_description()
                    
                    try:
                        # Extract relationships using LLM
                        response = self.llm_client.generate_structured_response(prompt, schema_description)
                        
                        if isinstance(response, list):
                            chunk_rels = []
                            for rel_data in response:
                                try:
                                    rel = EntityRelationship(**rel_data)
                                    chunk_rels.append(rel)
                                    doc_relationships.append(rel)
                                except Exception as e:
                                    logger.warning(f"Failed to parse relationship: {rel_data}")
                            
                            print(f"      ✓ Extracted {len(chunk_rels)} relationships")
                        
                    except Exception as e:
                        print(f"      ⚠️  Extraction failed: {e}")
                
                print(f"  ✅ Total relationships from {doc}: {len(doc_relationships)}")
                total_relationships += len(doc_relationships)
                self.extracted_relationships.extend(doc_relationships)
                
                # Step 4: Collect entities for deduplication analysis
                for rel in doc_relationships:
                    self.unique_entities.add(rel.subject.lower().strip())
                    self.unique_entities.add(rel.object.lower().strip())
                    
                    if rel.subject_type:
                        self.entity_types[rel.subject.lower().strip()] = rel.subject_type
                    if rel.object_type:
                        self.entity_types[rel.object.lower().strip()] = rel.object_type
            
            print(f"\n🎯 Pipeline Results:")
            print(f"  📊 Total relationships extracted: {total_relationships}")
            print(f"  👥 Unique entities discovered: {len(self.unique_entities)}")
            print(f"  🏷️  Entities with types: {len(self.entity_types)}")
            
            return total_relationships > 0
            
        except Exception as e:
            print(f"✗ Complete pipeline test failed: {e}")
            return False
    
    def analyze_graph_structure(self) -> bool:
        """Analyze the extracted graph structure for connectivity and quality."""
        print("🕸️  Analyzing Graph Structure")
        print("=" * 50)
        
        if not self.extracted_relationships:
            print("⚠️  No relationships to analyze")
            return True
        
        try:
            # Analyze entity connections
            entity_connections = {}
            relationship_types = set()
            
            for rel in self.extracted_relationships:
                source = rel.subject.lower().strip()
                target = rel.object.lower().strip()
                rel_type = rel.predicate
                
                # Track connections
                if source not in entity_connections:
                    entity_connections[source] = set()
                if target not in entity_connections:
                    entity_connections[target] = set()
                
                entity_connections[source].add(target)
                entity_connections[target].add(source)
                relationship_types.add(rel_type)
            
            # Calculate connectivity metrics
            connected_entities = sum(1 for conns in entity_connections.values() if conns)
            total_entities = len(entity_connections)
            avg_connections = sum(len(conns) for conns in entity_connections.values()) / total_entities if total_entities > 0 else 0
            
            print(f"✓ Graph Analysis Results:")
            print(f"  📈 Total entities: {total_entities}")
            print(f"  🔗 Connected entities: {connected_entities}")
            print(f"  📊 Average connections per entity: {avg_connections:.2f}")
            print(f"  🏷️  Relationship types: {len(relationship_types)}")
            
            # Show sample relationships
            print(f"\n📋 Sample Relationships:")
            for i, rel in enumerate(self.extracted_relationships[:5]):
                confidence = f" (confidence: {rel.confidence_score:.2f})" if rel.confidence_score else ""
                print(f"  {i+1}. {rel.subject} → {rel.predicate} → {rel.object}{confidence}")
            
            # Show sample entity types
            print(f"\n🏷️  Sample Entity Types:")
            for i, (entity, entity_type) in enumerate(list(self.entity_types.items())[:5]):
                print(f"  {i+1}. {entity} ({entity_type})")
            
            # Check for potential duplicates
            potential_duplicates = self._find_potential_duplicates()
            if potential_duplicates:
                print(f"\n⚠️  Potential duplicate entities found: {len(potential_duplicates)}")
                for dup in potential_duplicates[:3]:
                    print(f"    - '{dup[0]}' ≈ '{dup[1]}'")
            else:
                print(f"\n✅ No obvious duplicate entities detected")
            
            return True
            
        except Exception as e:
            print(f"✗ Graph analysis failed: {e}")
            return False
    
    def _find_potential_duplicates(self) -> List[tuple]:
        """Find potential duplicate entities."""
        duplicates = []
        entities = list(self.unique_entities)
        
        for i, entity1 in enumerate(entities):
            for entity2 in entities[i+1:]:
                if self._are_similar_entities(entity1, entity2):
                    duplicates.append((entity1, entity2))
        
        return duplicates
    
    def _are_similar_entities(self, entity1: str, entity2: str) -> bool:
        """Check if two entities are similar (potential duplicates)."""
        # Check if one is contained in the other
        if entity1 in entity2 or entity2 in entity1:
            return True
        
        # Check for common words
        words1 = set(entity1.split())
        words2 = set(entity2.split())
        
        if len(words1.intersection(words2)) >= min(len(words1), len(words2)) * 0.7:
            return True
        
        return False
    
    def _read_document_content(self, file_path: str) -> str:
        """Read document content based on file type."""
        file_extension = os.path.splitext(file_path)[1].lower()
        
        if file_extension == '.pdf':
            return self._read_pdf(file_path)
        elif file_extension == '.docx':
            return self._read_docx(file_path)
        elif file_extension == '.json':
            return self._read_json(file_path)
        else:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
    
    def _read_pdf(self, file_path: str) -> str:
        """Extract text from PDF file."""
        try:
            import pypdf
            with open(file_path, 'rb') as f:
                pdf_reader = pypdf.PdfReader(f)
                text_content = []
                for page in pdf_reader.pages:
                    text_content.append(page.extract_text())
                return '\n'.join(text_content)
        except ImportError:
            return ""
    
    def _read_docx(self, file_path: str) -> str:
        """Extract text from DOCX file."""
        try:
            from docx import Document
            doc = Document(file_path)
            text_content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            return '\n'.join(text_content)
        except ImportError:
            return ""
    
    def _read_json(self, file_path: str) -> str:
        """Extract and format content from JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            return json.dumps(json_data, indent=2, ensure_ascii=False)
        except json.JSONDecodeError:
            return ""


def main():
    """Main test execution function."""
    print("🚀 Complete Enhanced Pipeline Test")
    print("=" * 60)
    print("Demonstrating full enhanced features with OpenRouter LLM")
    print()
    
    tester = CompletePipelineTester()
    
    # Run tests
    tests = [
        ("Component Setup", tester.setup_components),
        ("Complete Pipeline", tester.test_complete_pipeline),
        ("Graph Analysis", tester.analyze_graph_structure),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"Running {test_name}...")
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            results[test_name] = False
        print()
    
    # Print summary
    print("📊 Complete Pipeline Test Summary")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status} {test_name}")
    
    print()
    print(f"Results: {passed}/{total} tests passed ({passed/total:.1%})")
    
    if passed == total:
        print("🎉 Complete enhanced pipeline working perfectly!")
        print("\n✅ Demonstrated Features:")
        print("  - Multi-format document processing (PDF, DOCX, JSON)")
        print("  - Advanced hybrid chunking strategy")
        print("  - LLM-powered entity and relationship extraction")
        print("  - Graph structure analysis and connectivity")
        print("  - Entity deduplication detection")
        print("  - Enhanced properties and metadata")
    else:
        print("⚠️  Some pipeline components failed.")
    
    return 0 if passed == total else 1


if __name__ == "__main__":
    sys.exit(main())
